import 'package:flutter/material.dart';
import '../models/employee_rpc.dart';
import '../services/auth_rpc_service.dart';

/// مزود المصادقة للعمل مع Odoo RPC
class AuthRpcProvider with ChangeNotifier {
  final AuthRpcService _authService = AuthRpcService();

  // حالة المصادقة
  bool _isLoggedIn = false;
  bool _isLoading = false;
  String? _errorMessage;
  
  // بيانات الموظف
  EmployeeRpc? _currentEmployee;
  String? _currentUid;
  bool _hasEmployeeData = false;

  // Getters
  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  EmployeeRpc? get currentEmployee => _currentEmployee;
  String? get currentUid => _currentUid;
  bool get hasEmployeeData => _hasEmployeeData;

  /// التحقق من حالة المصادقة عند بدء التطبيق
  Future<void> checkAuthStatus() async {
    _setLoading(true);

    try {
      _isLoggedIn = await _authService.isLoggedIn();
      
      if (_isLoggedIn) {
        _currentUid = await _authService.getCurrentUid();
        _currentEmployee = await _authService.getCurrentEmployee();
        _hasEmployeeData = _currentEmployee != null;
        
        if (_currentUid != null && _currentUid!.startsWith('mock_')) {
          _errorMessage = null; // البيانات التجريبية دائماً صحيحة
        }
      }
    } catch (e) {
      _errorMessage = 'خطأ في التحقق من حالة المصادقة: ${e.toString()}';
      _isLoggedIn = false;
    }

    _setLoading(false);
  }

  /// تسجيل الدخول
  /// 
  /// [username] - اسم المستخدم
  /// [password] - كلمة المرور  
  /// [database] - اسم قاعدة البيانات (اختياري للبيانات التجريبية)
  Future<bool> login(
    String username,
    String password, {
    String? database,
  }) async {
    _setLoading(true);
    _errorMessage = null;

    try {
      // محاولة تسجيل الدخول
      final result = await _authService.login(
        username,
        password,
        database: database,
      );

      if (result['success'] == true) {
        _isLoggedIn = true;
        _currentUid = result['uid'];
        _hasEmployeeData = result['has_employee_data'] ?? false;

        // جلب بيانات الموظف
        _currentEmployee = await _authService.getCurrentEmployee();
        
        // تحديث حالة وجود بيانات الموظف
        if (_currentEmployee != null) {
          _hasEmployeeData = true;
        }

        // رسالة نجاح مخصصة
        if (result['is_mock'] == true) {
          _errorMessage = null; // لا نعرض رسالة للبيانات التجريبية
        } else if (!_hasEmployeeData) {
          _errorMessage = 'تم تسجيل الدخول بنجاح، لكن لم يتم العثور على بيانات الموظف';
        } else {
          _errorMessage = null;
        }

        debugPrint('✅ تسجيل دخول ناجح - UID: $_currentUid');
        debugPrint('👤 بيانات الموظف: $_currentEmployee');
        
      } else {
        _isLoggedIn = false;
        _currentEmployee = null;
        _currentUid = null;
        _hasEmployeeData = false;
        _errorMessage = result['message'] ?? 'فشل في تسجيل الدخول';
        
        debugPrint('❌ فشل تسجيل الدخول: $_errorMessage');
      }
    } catch (e) {
      _isLoggedIn = false;
      _currentEmployee = null;
      _currentUid = null;
      _hasEmployeeData = false;
      _errorMessage = 'حدث خطأ أثناء تسجيل الدخول: ${e.toString()}';
      
      debugPrint('❌ خطأ في تسجيل الدخول: $e');
    }

    _setLoading(false);
    return _isLoggedIn;
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    _setLoading(true);

    try {
      await _authService.logout();
      
      _isLoggedIn = false;
      _currentEmployee = null;
      _currentUid = null;
      _hasEmployeeData = false;
      _errorMessage = null;
      
      debugPrint('🚪 تم تسجيل الخروج بنجاح');
    } catch (e) {
      _errorMessage = 'خطأ في تسجيل الخروج: ${e.toString()}';
      debugPrint('❌ خطأ في تسجيل الخروج: $e');
    }

    _setLoading(false);
  }

  /// إعادة جلب بيانات الموظف
  Future<bool> refreshEmployeeData() async {
    if (!_isLoggedIn) return false;

    _setLoading(true);
    _errorMessage = null;

    try {
      final result = await _authService.refreshEmployeeData();

      if (result['success'] == true) {
        _currentEmployee = await _authService.getCurrentEmployee();
        _hasEmployeeData = _currentEmployee != null;
        _errorMessage = null;
        
        debugPrint('🔄 تم تحديث بيانات الموظف: $_currentEmployee');
        
        _setLoading(false);
        return true;
      } else {
        _errorMessage = result['message'];
        
        // إذا كانت المشكلة في انتهاء صلاحية الجلسة
        if (result['requires_login'] == true) {
          _isLoggedIn = false;
          _currentEmployee = null;
          _currentUid = null;
          _hasEmployeeData = false;
        }

        debugPrint('❌ فشل تحديث بيانات الموظف: $_errorMessage');
        
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _errorMessage = 'خطأ في تحديث بيانات الموظف: ${e.toString()}';
      debugPrint('❌ خطأ في تحديث البيانات: $e');
      
      _setLoading(false);
      return false;
    }
  }

  /// التحقق من صحة الاتصال بالخادم
  Future<bool> checkServerConnection() async {
    try {
      return await _authService.checkServerConnection();
    } catch (e) {
      debugPrint('❌ خطأ في فحص الاتصال: $e');
      return false;
    }
  }

  /// الحصول على قائمة أسماء المستخدمين التجريبية
  List<String> getAvailableUsernames() {
    return _authService.getAvailableUsernames();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// تعيين حالة التحميل وإشعار المستمعين
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// معلومات إضافية للتطوير والتشخيص
  Map<String, dynamic> getDebugInfo() {
    return {
      'isLoggedIn': _isLoggedIn,
      'isLoading': _isLoading,
      'hasEmployeeData': _hasEmployeeData,
      'currentUid': _currentUid,
      'employeeName': _currentEmployee?.name,
      'managerName': _currentEmployee?.managerName,
      'errorMessage': _errorMessage,
    };
  }
}
