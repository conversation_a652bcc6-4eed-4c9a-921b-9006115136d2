import 'package:flutter_test/flutter_test.dart';
import 'package:bsic_bank/services/odoo_rpc_service.dart';
import 'package:bsic_bank/services/auth_rpc_service.dart';
import 'package:bsic_bank/models/employee_rpc.dart';
import 'package:bsic_bank/config/api_config.dart';

void main() {
  group('Odoo RPC Service Tests', () {
    late OdooRpcService rpcService;

    setUp(() {
      rpcService = OdooRpcService();
    });

    tearDown(() {
      rpcService.logout();
    });

    test('Test Server Connection', () async {
      final isConnected = await rpcService.checkServerConnection();
      
      print('Server connection test result: $isConnected');
      
      // هذا الاختبار قد يفشل إذا لم يكن الخادم متاحاً
      // لذلك نطبع النتيجة فقط بدلاً من التحقق الصارم
      expect(isConnected, isA<bool>());
    });

    test('Test Authentication with Invalid Credentials', () async {
      final result = await rpcService.authenticate(
        database: 'invalid_db',
        username: 'invalid_user',
        password: 'invalid_password',
      );

      print('Invalid auth result: $result');

      expect(result['success'], isFalse);
      expect(result['message'], isNotNull);
      expect(rpcService.isAuthenticated, isFalse);
    });

    test('Test Authentication with Valid Credentials', () async {
      // تحديث هذه القيم حسب بيئة الاختبار
      const testDatabase = 'test_db';
      const testUsername = 'admin';
      const testPassword = 'admin';

      final result = await rpcService.authenticate(
        database: testDatabase,
        username: testUsername,
        password: testPassword,
      );

      print('Valid auth result: $result');

      if (result['success'] == true) {
        expect(result['uid'], isNotNull);
        expect(rpcService.isAuthenticated, isTrue);
        expect(rpcService.uid, isNotNull);
        expect(rpcService.database, equals(testDatabase));

        // اختبار جلب بيانات الموظف
        final employeeResult = await rpcService.getCurrentEmployeeData();
        print('Employee data result: $employeeResult');

        if (employeeResult['success'] == true) {
          expect(employeeResult['data'], isNotNull);
          final employeeData = employeeResult['data'];
          expect(employeeData['id'], isNotNull);
          expect(employeeData['name'], isNotNull);
        } else {
          print('Employee data not found (expected if user not linked to employee)');
        }
      } else {
        print('Authentication failed (expected if no server available)');
      }
    });

    test('Test Employee Data without Authentication', () async {
      final result = await rpcService.getCurrentEmployeeData();

      print('Unauthenticated employee data result: $result');

      expect(result['success'], isFalse);
      expect(result['requires_login'], isTrue);
    });
  });

  group('Employee RPC Model Tests', () {
    test('Test EmployeeRpc.fromOdooRpc with full data', () {
      final testData = {
        'id': 123,
        'name': 'أحمد علي',
        'parent_id': [456, 'محمد حسن'],
      };

      final employee = EmployeeRpc.fromOdooRpc(testData);

      expect(employee.id, equals(123));
      expect(employee.name, equals('أحمد علي'));
      expect(employee.managerId, equals(456));
      expect(employee.managerName, equals('محمد حسن'));
      expect(employee.hasManager, isTrue);
      expect(employee.managerDisplayText, equals('محمد حسن'));
    });

    test('Test EmployeeRpc.fromOdooRpc without manager', () {
      final testData = {
        'id': 123,
        'name': 'سارة أحمد',
        'parent_id': false,
      };

      final employee = EmployeeRpc.fromOdooRpc(testData);

      expect(employee.id, equals(123));
      expect(employee.name, equals('سارة أحمد'));
      expect(employee.managerId, isNull);
      expect(employee.managerName, isNull);
      expect(employee.hasManager, isFalse);
      expect(employee.managerDisplayText, equals('لا يوجد مدير مباشر'));
    });

    test('Test EmployeeRpc.toJson', () {
      final employee = EmployeeRpc(
        id: 123,
        name: 'عمر حسن',
        managerId: 456,
        managerName: 'فاطمة محمد',
      );

      final json = employee.toJson();

      expect(json['id'], equals(123));
      expect(json['name'], equals('عمر حسن'));
      expect(json['manager_id'], equals(456));
      expect(json['manager_name'], equals('فاطمة محمد'));
    });
  });

  group('Auth RPC Service Tests', () {
    late AuthRpcService authService;

    setUp(() {
      authService = AuthRpcService();
    });

    tearDown(() async {
      await authService.logout();
    });

    test('Test Mock Login', () async {
      final result = await authService.login('admin', 'admin123');

      print('Mock login result: $result');

      expect(result['success'], isTrue);
      expect(result['is_mock'], isTrue);
      expect(result['uid'], equals('mock_admin'));

      // اختبار الحصول على الموظف التجريبي
      final employee = await authService.getCurrentEmployee();
      expect(employee, isNotNull);
      expect(employee!.name, isNotEmpty);

      // اختبار حالة تسجيل الدخول
      final isLoggedIn = await authService.isLoggedIn();
      expect(isLoggedIn, isTrue);

      final uid = await authService.getCurrentUid();
      expect(uid, equals('mock_admin'));
    });

    test('Test Mock Login with Invalid Credentials', () async {
      final result = await authService.login('invalid', 'invalid');

      print('Invalid mock login result: $result');

      expect(result['success'], isFalse);
      expect(result['message'], contains('غير صحيحة'));
    });

    test('Test Real API Login (may fail without server)', () async {
      final result = await authService.login(
        'admin',
        'admin',
        database: 'test_db',
      );

      print('Real API login result: $result');

      // هذا الاختبار قد يفشل إذا لم يكن الخادم متاحاً
      expect(result['success'], isA<bool>());
      expect(result['message'], isNotNull);
    });

    test('Test Logout', () async {
      // تسجيل دخول تجريبي أولاً
      await authService.login('admin', 'admin123');
      
      // التحقق من تسجيل الدخول
      expect(await authService.isLoggedIn(), isTrue);
      
      // تسجيل الخروج
      await authService.logout();
      
      // التحقق من تسجيل الخروج
      expect(await authService.isLoggedIn(), isFalse);
      expect(await authService.getCurrentUid(), isNull);
      expect(await authService.getCurrentEmployee(), isNull);
    });

    test('Test Available Usernames', () {
      final usernames = authService.getAvailableUsernames();

      expect(usernames, isNotEmpty);
      expect(usernames, contains('admin'));
      expect(usernames, contains('ahmed.ali'));
      expect(usernames, contains('sara.mohamed'));
      expect(usernames, contains('omar.hassan'));
    });

    test('Test Server Connection Check', () async {
      final isConnected = await authService.checkServerConnection();

      print('Auth service connection test: $isConnected');

      expect(isConnected, isA<bool>());
    });
  });

  group('API Configuration Tests', () {
    test('Test API Config Values', () {
      expect(ApiConfig.baseUrl, isNotEmpty);
      expect(ApiConfig.connectionTimeout, isNotNull);
      expect(ApiConfig.receiveTimeout, isNotNull);
      expect(ApiConfig.defaultHeaders, isNotEmpty);
      expect(ApiConfig.defaultHeaders['Content-Type'], equals('application/json'));
    });

    test('Test getFullUrl', () {
      final fullUrl = ApiConfig.getFullUrl('/test/endpoint');
      expect(fullUrl, contains(ApiConfig.baseUrl));
      expect(fullUrl, endsWith('/test/endpoint'));
    });

    test('Test isValidServerUrl', () {
      expect(ApiConfig.isValidServerUrl('http://example.com'), isTrue);
      expect(ApiConfig.isValidServerUrl('https://example.com:8069'), isTrue);
      expect(ApiConfig.isValidServerUrl('ftp://example.com'), isTrue);
      expect(ApiConfig.isValidServerUrl('invalid-url'), isFalse);
      expect(ApiConfig.isValidServerUrl(''), isFalse);
    });
  });

  group('Integration Tests', () {
    test('Test Complete Flow with Mock Data', () async {
      final authService = AuthRpcService();

      try {
        // 1. تسجيل الدخول
        final loginResult = await authService.login('admin', 'admin123');
        expect(loginResult['success'], isTrue);

        // 2. التحقق من حالة تسجيل الدخول
        expect(await authService.isLoggedIn(), isTrue);

        // 3. جلب بيانات الموظف
        final employee = await authService.getCurrentEmployee();
        expect(employee, isNotNull);
        expect(employee!.name, isNotEmpty);

        // 4. إعادة جلب البيانات
        final refreshResult = await authService.refreshEmployeeData();
        expect(refreshResult['success'], isTrue);

        // 5. تسجيل الخروج
        await authService.logout();
        expect(await authService.isLoggedIn(), isFalse);

        print('✅ Complete flow test passed');
      } catch (e) {
        print('❌ Complete flow test failed: $e');
        rethrow;
      }
    });
  });
}
