# دليل Odoo RPC Integration - الدليل الشامل

## نظرة عامة

تم إنشاء نظام كامل للربط مع Odoo باستخدام JSON-RPC كما طلبت. النظام يدعم:

- ✅ تسجيل الدخول باستخدام `authenticate` للحصول على UID
- ✅ جلب بيانات الموظف باستخدام `execute_kw` على موديل `hr.employee`
- ✅ حماية البيانات باستخدام UID وRecord Rules
- ✅ دعم البيانات التجريبية كـ fallback
- ✅ واجهة مستخدم كاملة باللغة العربية

## الملفات الجديدة

### 1. الخدمات (Services)
- **`lib/services/odoo_rpc_service.dart`** - خدمة Odoo RPC الأساسية
- **`lib/services/auth_rpc_service.dart`** - خدمة المصادقة المحدثة

### 2. النماذج (Models)
- **`lib/models/employee_rpc.dart`** - نموذج بيانات الموظف للـ RPC

### 3. المزودات (Providers)
- **`lib/providers/auth_rpc_provider.dart`** - مزود المصادقة للـ RPC

### 4. الشاشات (Screens)
- **`lib/screens/login_rpc_screen.dart`** - شاشة تسجيل الدخول المحدثة
- **`lib/screens/employee_rpc_screen.dart`** - شاشة عرض بيانات الموظف

### 5. التطبيق الرئيسي
- **`lib/main_rpc.dart`** - التطبيق الرئيسي المحدث

### 6. الاختبارات
- **`test/odoo_rpc_test.dart`** - اختبارات شاملة للنظام

## كيفية الاستخدام

### 1. تحديث إعدادات الخادم

```dart
// في lib/config/api_config.dart
static const String baseUrl = 'http://your-odoo-server:8069';
```

### 2. تشغيل التطبيق

```bash
# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق الجديد
flutter run lib/main_rpc.dart
```

### 3. تسجيل الدخول

#### للاستخدام الحقيقي:
1. فعّل "استخدام API الحقيقي"
2. أدخل اسم قاعدة البيانات
3. أدخل اسم المستخدم وكلمة المرور
4. اضغط "تسجيل الدخول"

#### للاختبار:
1. اترك "استخدام API الحقيقي" مُعطل
2. اختر أحد أسماء المستخدمين التجريبية
3. اضغط "تسجيل الدخول"

## التفاصيل التقنية

### 1. تسجيل الدخول (Authentication)

```dart
// استدعاء authenticate للحصول على UID
final requestData = {
  'jsonrpc': '2.0',
  'method': 'call',
  'params': {
    'service': 'common',
    'method': 'authenticate',
    'args': [database, username, password, {}]
  },
  'id': DateTime.now().millisecondsSinceEpoch,
};
```

**الاستجابة المتوقعة:**
```json
{
  "jsonrpc": "2.0",
  "result": 123,  // UID المستخدم
  "id": 1234567890
}
```

### 2. جلب بيانات الموظف

```dart
// استدعاء execute_kw لجلب بيانات الموظف
final requestData = {
  'jsonrpc': '2.0',
  'method': 'call',
  'params': {
    'service': 'object',
    'method': 'execute_kw',
    'args': [
      database,
      uid,
      password,
      'hr.employee',
      'search_read',
      [
        [['user_id', '=', uid]] // البحث عن الموظف المرتبط بالمستخدم
      ],
      {
        'fields': ['name', 'parent_id'], // الحقول المطلوبة
        'limit': 1,
      }
    ]
  },
  'id': DateTime.now().millisecondsSinceEpoch,
};
```

**الاستجابة المتوقعة:**
```json
{
  "jsonrpc": "2.0",
  "result": [
    {
      "id": 456,
      "name": "أحمد علي",
      "parent_id": [789, "محمد حسن"]
    }
  ],
  "id": 1234567890
}
```

### 3. الحماية والأمان

- **UID Validation**: كل طلب يتطلب UID صالح
- **Record Rules**: Odoo يطبق قواعد الوصول تلقائياً
- **User Linking**: يتم جلب بيانات الموظف المرتبط بالمستخدم فقط
- **Session Management**: حفظ UID محلياً للطلبات اللاحقة

## الميزات المتقدمة

### 1. معالجة الأخطاء

```dart
// التحقق من أخطاء JSON-RPC
if (responseData.containsKey('error')) {
  final error = responseData['error'];
  String errorMessage = 'حدث خطأ';
  
  if (error is Map && error.containsKey('data')) {
    final errorData = error['data'];
    if (errorData is Map && errorData.containsKey('message')) {
      errorMessage = errorData['message'];
    }
  }
  
  return {'success': false, 'message': errorMessage};
}
```

### 2. إعادة المحاولة والتحقق من الاتصال

```dart
// التحقق من صحة الاتصال
Future<bool> checkServerConnection() async {
  try {
    final requestData = {
      'jsonrpc': '2.0',
      'method': 'call',
      'params': {
        'service': 'common',
        'method': 'version',
        'args': []
      },
      'id': 1,
    };
    
    final response = await http.post(url, body: jsonEncode(requestData));
    return response.statusCode == 200;
  } catch (e) {
    return false;
  }
}
```

### 3. البيانات التجريبية (Fallback)

```dart
// أسماء مستخدمين تجريبية
static const Map<String, String> _mockCredentials = {
  'admin': 'admin123',
  'ahmed.ali': 'password123',
  'sara.mohamed': 'password123',
  'omar.hassan': 'password123',
};
```

## الاختبار

### تشغيل الاختبارات:

```bash
flutter test test/odoo_rpc_test.dart
```

### اختبارات متاحة:
- ✅ اختبار الاتصال بالخادم
- ✅ اختبار المصادقة (صحيحة وخاطئة)
- ✅ اختبار جلب بيانات الموظف
- ✅ اختبار البيانات التجريبية
- ✅ اختبار تسجيل الخروج
- ✅ اختبار التكامل الكامل

## استكشاف الأخطاء

### 1. مشاكل الاتصال
```
❌ خطأ: انتهت مهلة الاتصال
✅ الحل: تحقق من baseUrl وحالة الخادم
```

### 2. مشاكل المصادقة
```
❌ خطأ: اسم المستخدم أو كلمة المرور غير صحيحة
✅ الحل: تحقق من بيانات تسجيل الدخول وقاعدة البيانات
```

### 3. مشاكل بيانات الموظف
```
❌ خطأ: لم يتم العثور على بيانات الموظف
✅ الحل: تأكد من ربط المستخدم بسجل موظف في Odoo
```

## API Key المستخدم

```
88fca631891c8a6727da95fc798cbe57cc00748d
```

## الخطوات التالية

### للتطوير:
1. إضافة المزيد من الحقول لبيانات الموظف
2. إضافة APIs إضافية (الإجازات، الحضور، إلخ)
3. تحسين واجهة المستخدم
4. إضافة إشعارات push

### للنشر:
1. تحديث `baseUrl` للخادم الحقيقي
2. تعطيل `enableDebugMode`
3. اختبار شامل مع بيانات حقيقية
4. مراجعة أمنية نهائية

## الدعم

في حالة وجود مشاكل:
1. تحقق من logs في وضع debug
2. اختبر الاتصال بالخادم
3. تأكد من صحة إعدادات Odoo
4. راجع ملفات الاختبار للأمثلة

---

**✅ النظام جاهز للاستخدام!**

جميع الملفات تم إنشاؤها وهي جاهزة للتشغيل. يمكنك الآن تشغيل التطبيق واختبار الربط مع Odoo باستخدام JSON-RPC.
