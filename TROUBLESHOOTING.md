# استكشاف الأخطاء وحلها - Troubleshooting

## 🔧 الأخطاء الشائعة وحلولها

### 1. أخطاء التشغيل (Runtime Errors)

#### خطأ: `textDirection` isn't defined
```
❌ الخطأ: The named parameter 'textDirection' isn't defined
✅ الحل: تم إصلاحه - إزالة textDirection من MaterialApp
```

#### خطأ: `CardTheme` can't be assigned
```
❌ الخطأ: The argument type 'CardTheme' can't be assigned to 'CardThemeData?'
✅ الحل: تم إصلاحه - تغيير CardTheme إلى CardThemeData
```

### 2. أخطاء الاتصال (Connection Errors)

#### خطأ: انتهت مهلة الاتصال
```
❌ الخطأ: انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى
✅ الحل:
  1. تحقق من baseUrl في lib/config/api_config.dart
  2. تأكد من تشغيل خادم Odoo
  3. اختبر الرابط في المتصفح: http://your-server:8069
  4. تحقق من إعدادات الشبكة والجدار الناري
```

#### خطأ: فشل في الاتصال بالخادم
```
❌ الخطأ: خطأ في الاتصال بالخادم (404/500/etc)
✅ الحل:
  1. تحقق من صحة رابط الخادم
  2. تأكد من أن Odoo يعمل على المنفذ الصحيح
  3. جرب زر "اختبار الاتصال" في التطبيق
```

### 3. أخطاء المصادقة (Authentication Errors)

#### خطأ: اسم المستخدم أو كلمة المرور غير صحيحة
```
❌ الخطأ: فشل في تسجيل الدخول
✅ الحل:
  1. تحقق من صحة اسم المستخدم وكلمة المرور
  2. تأكد من صحة اسم قاعدة البيانات
  3. تحقق من وجود المستخدم في Odoo
  4. جرب البيانات التجريبية للاختبار
```

#### خطأ: قاعدة البيانات غير موجودة
```
❌ الخطأ: Database does not exist
✅ الحل:
  1. تحقق من اسم قاعدة البيانات في Odoo
  2. تأكد من أن قاعدة البيانات نشطة
  3. جرب قائمة قواعد البيانات: http://your-server:8069/web/database/selector
```

### 4. أخطاء بيانات الموظف (Employee Data Errors)

#### خطأ: لم يتم العثور على بيانات الموظف
```
❌ الخطأ: لم يتم العثور على بيانات الموظف
✅ الحل:
  1. تأكد من ربط المستخدم بسجل موظف في Odoo
  2. تحقق من صلاحيات المستخدم للوصول لموديل hr.employee
  3. تأكد من وجود حقل user_id في سجل الموظف
  4. جرب البيانات التجريبية للاختبار
```

#### خطأ: الموظف غير مرتبط بالمستخدم الحالي
```
❌ الخطأ: الموظف غير مرتبط بالمستخدم الحالي
✅ الحل:
  1. في Odoo، اذهب إلى Employees
  2. افتح سجل الموظف
  3. في تبويب HR Settings، اربط الموظف بالمستخدم
  4. احفظ التغييرات
```

### 5. أخطاء Flutter (Flutter Errors)

#### خطأ: Flutter command not found
```
❌ الخطأ: 'flutter' is not recognized
✅ الحل:
  1. تأكد من تثبيت Flutter SDK
  2. أضف Flutter إلى PATH
  3. شغّل flutter doctor للتحقق
```

#### خطأ: Package dependencies
```
❌ الخطأ: Package not found
✅ الحل:
  1. شغّل flutter pub get
  2. تحقق من ملف pubspec.yaml
  3. امسح .packages و pubspec.lock وأعد التثبيت
```

## 🧪 أدوات التشخيص

### 1. فحص التطبيق
```bash
# فحص شامل للكود
flutter analyze

# فحص ملف معين
flutter analyze lib/main_rpc.dart

# أو استخدم ملف الفحص
check_app.bat
```

### 2. اختبار الاتصال
```bash
# اختبار الاتصال بالخادم
curl http://your-server:8069/jsonrpc

# أو في المتصفح
http://your-server:8069/web/database/selector
```

### 3. تشغيل الاختبارات
```bash
# اختبارات Odoo RPC
flutter test test/odoo_rpc_test.dart

# جميع الاختبارات
flutter test

# أو استخدم ملف الاختبار
run_tests.bat
```

## 📱 اختبار البيانات التجريبية

إذا كانت هناك مشاكل مع Odoo، جرب البيانات التجريبية:

```
اسم المستخدم: admin
كلمة المرور: admin123
قاعدة البيانات: اتركها فارغة
```

## 🔍 تفعيل وضع التطوير

لرؤية تفاصيل أكثر عن الأخطاء:

```dart
// في lib/config/api_config.dart
static const bool enableDebugMode = true;
```

هذا سيظهر:
- تفاصيل طلبات HTTP
- استجابات الخادم
- رسائل تشخيصية مفصلة

## 📞 الحصول على المساعدة

### 1. معلومات النظام
```bash
flutter doctor -v
flutter --version
```

### 2. معلومات التطبيق
- راجع ملف `ODOO_RPC_GUIDE.md`
- تحقق من `QUICK_START.md`
- اقرأ `README.md`

### 3. معلومات الخطأ
عند طلب المساعدة، قدم:
- رسالة الخطأ كاملة
- خطوات إعادة إنتاج المشكلة
- معلومات البيئة (Flutter version, OS, etc.)
- إعدادات الخادم (إن أمكن)

## ✅ قائمة التحقق السريع

قبل طلب المساعدة، تأكد من:

- [ ] تشغيل `flutter pub get`
- [ ] تشغيل `flutter doctor`
- [ ] تحديث `baseUrl` في api_config.dart
- [ ] اختبار البيانات التجريبية
- [ ] تفعيل وضع debug
- [ ] قراءة رسائل الخطأ بعناية

---

**💡 نصيحة: ابدأ دائماً بالبيانات التجريبية للتأكد من أن التطبيق يعمل، ثم انتقل للاختبار مع Odoo الحقيقي.**
