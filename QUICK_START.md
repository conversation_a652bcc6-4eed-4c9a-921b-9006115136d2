# البدء السريع - Quick Start

## 🚀 تشغيل التطبيق فوراً

### الطريقة الأولى: استخدام ملفات التشغيل
```bash
# في Windows
run_rpc_app.bat

# أو تشغيل الاختبارات
run_tests.bat
```

### الطريقة الثانية: الأوامر المباشرة
```bash
# تثبيت التبعيات
flutter pub get

# تشغيل تطبيق Odoo RPC
flutter run lib/main_rpc.dart

# تشغيل الاختبارات
flutter test test/odoo_rpc_test.dart
```

## ⚙️ الإعداد السريع

### 1. تحديث رابط الخادم
افتح `lib/config/api_config.dart` وغيّر:
```dart
static const String baseUrl = 'http://your-odoo-server:8069';
```

### 2. اختبار سريع بالبيانات التجريبية
1. شغّل التطبيق
2. اترك "استخدام API الحقيقي" مُعطل
3. اختر `admin` من القائمة
4. اضغط "تسجيل الدخول"

### 3. اختبار مع Odoo الحقيقي
1. شغّل التطبيق
2. فعّل "استخدام API الحقيقي"
3. أدخل اسم قاعدة البيانات
4. أدخل اسم المستخدم وكلمة المرور
5. اضغط "تسجيل الدخول"

## 🔧 استكشاف الأخطاء السريع

### خطأ: لا يمكن الاتصال بالخادم
```
✅ الحل: تحقق من baseUrl في api_config.dart
✅ تأكد من تشغيل خادم Odoo
✅ جرب زر "اختبار الاتصال"
```

### خطأ: فشل تسجيل الدخول
```
✅ الحل: تحقق من اسم قاعدة البيانات
✅ تأكد من صحة اسم المستخدم وكلمة المرور
✅ تحقق من وجود المستخدم في Odoo
```

### خطأ: لا توجد بيانات موظف
```
✅ الحل: تأكد من ربط المستخدم بسجل موظف في Odoo
✅ تحقق من صلاحيات المستخدم
✅ جرب البيانات التجريبية للاختبار
```

## 📱 أسماء المستخدمين التجريبية

| اسم المستخدم | كلمة المرور | الوصف |
|---------------|-------------|--------|
| `admin` | `admin123` | مدير النظام |
| `ahmed.ali` | `password123` | موظف عادي |
| `sara.mohamed` | `password123` | موظفة عادية |
| `omar.hassan` | `password123` | موظف عادي |

## 🔑 API Key

```
88fca631891c8a6727da95fc798cbe57cc00748d
```

## 📚 المراجع السريعة

- **الدليل الشامل**: `ODOO_RPC_GUIDE.md`
- **تحديثات الأمان**: `SECURITY_API_UPDATE.md`
- **أمثلة APIs**: `FUTURE_APIS_EXAMPLE.md`
- **ملخص التنفيذ**: `IMPLEMENTATION_SUMMARY.md`

## 🆘 الدعم السريع

1. **مشكلة في التشغيل؟** راجع `ODOO_RPC_GUIDE.md`
2. **أخطاء في الكود؟** شغّل `flutter doctor`
3. **مشاكل في الاتصال؟** اختبر `baseUrl` في المتصفح
4. **بيانات لا تظهر؟** جرب البيانات التجريبية أولاً

---

**🎉 مبروك! التطبيق جاهز للاستخدام**
