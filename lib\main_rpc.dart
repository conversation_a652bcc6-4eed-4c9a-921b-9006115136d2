import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'providers/auth_rpc_provider.dart';
import 'utils/app_colors.dart';
import 'screens/login_rpc_screen.dart';
import 'screens/employee_rpc_screen.dart';

/// التطبيق الرئيسي للعمل مع Odoo RPC
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await initializeDateFormatting('ar', null);
  runApp(const EmployeeRpcApp());
}

class EmployeeRpcApp extends StatelessWidget {
  const EmployeeRpcApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthRpcProvider()),
      ],
      child: MaterialApp(
        title: 'نظام إدارة الموظفين - Odoo RPC',
        theme: AppTheme.lightTheme,
        debugShowCheckedModeBanner: false,
        home: const AppNavigator(),
        // دعم اللغة العربية
        locale: const Locale('ar'),
      ),
    );
  }
}

/// المتحكم الرئيسي في التنقل
class AppNavigator extends StatefulWidget {
  const AppNavigator({super.key});

  @override
  State<AppNavigator> createState() => _AppNavigatorState();
}

class _AppNavigatorState extends State<AppNavigator> {
  bool _isLoggedIn = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  /// التحقق من حالة المصادقة عند بدء التطبيق
  Future<void> _checkAuthStatus() async {
    final authProvider = Provider.of<AuthRpcProvider>(context, listen: false);
    
    await authProvider.checkAuthStatus();
    
    setState(() {
      _isLoggedIn = authProvider.isLoggedIn;
      _isLoading = false;
    });
  }

  /// عند نجاح تسجيل الدخول
  void _onLoginSuccess() {
    setState(() {
      _isLoggedIn = true;
    });
  }

  /// عند تسجيل الخروج
  void _onLogout() {
    setState(() {
      _isLoggedIn = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    // شاشة تحميل أثناء التحقق من حالة المصادقة
    if (_isLoading) {
      return Scaffold(
        backgroundColor: AppColors.background,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // شعار التطبيق
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.business,
                  size: 60,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 24),
              
              // اسم التطبيق
              Text(
                'نظام إدارة الموظفين',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              
              Text(
                'Odoo RPC Integration',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 32),
              
              // مؤشر التحميل
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
              const SizedBox(height: 16),
              
              const Text('جاري التحقق من حالة تسجيل الدخول...'),
            ],
          ),
        ),
      );
    }

    // التنقل بين شاشة تسجيل الدخول وشاشة البيانات
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: _isLoggedIn
        ? EmployeeRpcScreen(
            key: const ValueKey('employee_screen'),
            onLogout: _onLogout,
          )
        : LoginRpcScreen(
            key: const ValueKey('login_screen'),
            onLoginSuccess: _onLoginSuccess,
          ),
    );
  }
}

/// ثيم التطبيق
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: Colors.blue,
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: AppColors.background,
      
      // شريط التطبيق
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      
      // الأزرار
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      
      // البطاقات
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.symmetric(vertical: 4),
      ),
      
      // حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      
      // النصوص
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
        headlineMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
        headlineSmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: Colors.black87,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: Colors.black54,
        ),
      ),
      
      // الألوان
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.light,
      ),
      
      // دعم اللغة العربية
      fontFamily: 'Arial',
    );
  }
}
