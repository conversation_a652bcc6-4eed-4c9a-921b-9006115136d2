# تحديث API الحماية - Security API Update

## نظرة عامة

تم تحديث التطبيق لاستخدام API الحماية الجديد الذي يستخدم مصادقة Odoo المدمجة (`auth='user'`) بدلاً من JSON-RPC المعقد.

## التحديثات المنجزة

### 1. تحديث `lib/services/api_service.dart`

#### أ. إضافة دالة تسجيل دخول جديدة
```dart
static Future<Map<String, dynamic>> loginOdoo({
  required String database,
  required String username,
  required String password,
}) async
```

**الميزات:**
- استخدام `/web/session/authenticate` endpoint
- استخراج `session_id` من cookies
- معالجة أفضل للأخطاء
- دعم كامل لمصادقة Odoo

#### ب. تحديث دالة جلب بيانات الموظف
```dart
static Future<Map<String, dynamic>> getEmployeeData({
  required String sessionId,
  String? database,
  String? userId,
}) async
```

**التحسينات:**
- استخدام GET request بدلاً من POST
- إرسال `session_id` في Cookie header
- إضافة `X-Openerp-Dbname` header للقاعدة
- معالجة مبسطة للاستجابة (بدون JSON-RPC)
- دعم مباشر لتنسيق API الجديد

### 2. تحديث `lib/services/auth_service.dart`

#### تحديث دالة تسجيل الدخول
- استخدام `ApiService.loginOdoo()` بدلاً من `ApiService.login()`
- الحفاظ على التوافق مع البيانات التجريبية

## API الجديد المدعوم

### Endpoint: `/api/employee/me`
```python
@http.route('/api/employee/me', type='http', auth='user', csrf=False)
def get_employee(self):
    employee = request.env.user.employee_id
    
    if not employee:
        return request.make_response(
            json.dumps({'error': 'الموظف غير مرتبط بالمستخدم الحالي'}),
            headers=[('Content-Type', 'application/json')]
        )

    result = {
        'int_id': employee.int_id,
        'name': employee.name,
        'department_id': employee.department_id and [employee.department_id.id, employee.department_id.name],
        'parent_id': employee.parent_id and [employee.parent_id.id, employee.parent_id.name],
        'coach_id': employee.coach_id and [employee.coach_id.id, employee.coach_id.name],
        'connected_with_comp': employee.connected_with_comp,
        'national_number': employee.national_number,
    }

    return request.make_response(json.dumps(result), headers=[('Content-Type', 'application/json')])
```

### تنسيق الاستجابة المتوقعة
```json
{
  "int_id": "EMP001",
  "name": "أحمد علي",
  "department_id": [1, "قسم تقنية المعلومات"],
  "parent_id": [2, "محمد حسن"],
  "coach_id": [3, "سارة أحمد"],
  "connected_with_comp": true,
  "national_number": "1234567890"
}
```

## كيفية الاستخدام

### 1. تكوين الخادم
تأكد من أن `lib/config/api_config.dart` يحتوي على الرابط الصحيح:
```dart
static const String baseUrl = 'http://your-odoo-server:8069';
```

### 2. تسجيل الدخول
1. في شاشة تسجيل الدخول، فعّل "استخدام API الحقيقي"
2. أدخل اسم قاعدة البيانات
3. أدخل اسم المستخدم وكلمة المرور
4. سيتم تسجيل الدخول تلقائياً وجلب بيانات الموظف

### 3. الحماية
- يستخدم API مصادقة Odoo المدمجة
- يتطلب session صالح للوصول للبيانات
- يتحقق من ربط المستخدم بموظف في النظام

## معالجة الأخطاء

### الأخطاء المحتملة:
1. **"الموظف غير مرتبط بالمستخدم الحالي"**: المستخدم غير مرتبط بسجل موظف
2. **"انتهت صلاحية الجلسة"**: يحتاج إعادة تسجيل دخول
3. **"API endpoint غير موجود"**: تحقق من إعدادات الخادم

### التعامل مع الأخطاء:
- عرض رسائل خطأ واضحة للمستخدم
- إعادة توجيه لشاشة تسجيل الدخول عند انتهاء الجلسة
- استخدام البيانات التجريبية كـ fallback

## الاختبار

### اختبار API:
1. تأكد من تشغيل خادم Odoo
2. تأكد من وجود API endpoint `/api/employee/me`
3. اختبر تسجيل الدخول بمستخدم مرتبط بموظف
4. تحقق من جلب البيانات بنجاح

### اختبار البيانات التجريبية:
- اترك حقل قاعدة البيانات فارغاً
- استخدم أحد أسماء المستخدمين التجريبية
- تأكد من عمل التطبيق بدون اتصال بالخادم

## الملاحظات الفنية

1. **التوافق**: الكود يدعم كلاً من API الجديد والبيانات التجريبية
2. **الأمان**: استخدام مصادقة Odoo المدمجة يوفر حماية أفضل
3. **البساطة**: إزالة تعقيد JSON-RPC يجعل الكود أبسط وأكثر موثوقية
4. **المرونة**: يمكن إضافة endpoints جديدة بسهولة

## التطوير المستقبلي

يمكن إضافة المزيد من APIs باستخدام نفس النمط:
- `/api/employee/leaves` - للإجازات
- `/api/employee/attendance` - للحضور والانصراف
- `/api/employee/payslip` - لقسائم الراتب

جميع هذه APIs ستستخدم نفس نظام المصادقة (`auth='user'`) وتنسيق الاستجابة البسيط.
