import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';

class ApiService {
  /// تسجيل الدخول باستخدام Odoo Web Login
  static Future<Map<String, dynamic>> loginOdoo({
    required String database,
    required String username,
    required String password,
  }) async {
    try {
      final url = Uri.parse('${ApiConfig.baseUrl}/web/session/authenticate');

      final body = jsonEncode({
        'jsonrpc': '2.0',
        'method': 'call',
        'params': {
          'db': database,
          'login': username,
          'password': password,
        },
        'id': null,
      });

      if (ApiConfig.enableDebugMode) {
        print('Sending Odoo login request to: $url');
        print('Request body: $body');
      }

      final response = await http
          .post(url, headers: ApiConfig.defaultHeaders, body: body)
          .timeout(
            ApiConfig.connectionTimeout,
            onTimeout: () {
              throw Exception('انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.');
            },
          );

      if (ApiConfig.enableDebugMode) {
        print('Odoo login response status: ${response.statusCode}');
        print('Odoo login response body: ${response.body}');
        print('Odoo login response headers: ${response.headers}');
      }

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (ApiConfig.enableDebugMode) {
          print('Parsed Odoo login response: $responseData');
        }

        // التحقق من وجود خطأ في الاستجابة
        if (responseData.containsKey('error')) {
          return {
            'success': false,
            'message': responseData['error']['message'] ?? 'حدث خطأ في تسجيل الدخول',
          };
        }

        // التحقق من نجاح العملية
        if (responseData.containsKey('result')) {
          final result = responseData['result'];

          if (result != null && result is Map<String, dynamic>) {
            // استخراج session_id من cookies
            String? sessionId;
            final setCookieHeader = response.headers['set-cookie'];
            if (setCookieHeader != null) {
              final cookies = setCookieHeader.split(',');
              for (final cookie in cookies) {
                if (cookie.trim().startsWith('session_id=')) {
                  sessionId = cookie.trim().split('=')[1].split(';')[0];
                  break;
                }
              }
            }

            if (ApiConfig.enableDebugMode) {
              print('Extracted session_id: $sessionId');
              print('User ID from result: ${result['uid']}');
            }

            if (sessionId != null && result['uid'] != null) {
              return {
                'success': true,
                'message': 'تم تسجيل الدخول بنجاح',
                'session_id': sessionId,
                'user_id': result['uid'],
              };
            } else {
              return {
                'success': false,
                'message': 'فشل في الحصول على معلومات الجلسة',
              };
            }
          }
        }

        return {'success': false, 'message': 'استجابة غير متوقعة من الخادم'};
      } else {
        return {
          'success': false,
          'message': 'خطأ في الاتصال بالخادم (${response.statusCode})',
        };
      }
    } catch (e) {
      if (ApiConfig.enableDebugMode) {
        print('Odoo login error: $e');
      }
      return {'success': false, 'message': 'خطأ في الاتصال: ${e.toString()}'};
    }
  }

  /// تسجيل الدخول باستخدام API (الطريقة القديمة - للتوافق مع الإصدارات السابقة)
  static Future<Map<String, dynamic>> login({
    required String database,
    required String username,
    required String password,
  }) async {
    try {
      final url = Uri.parse(ApiConfig.getFullUrl(ApiConfig.loginEndpoint));

      final body = jsonEncode({
        'jsonrpc': '2.0',
        'method': 'call',
        'params': {'db': database, 'login': username, 'password': password},
        'id': null,
      });

      if (ApiConfig.enableDebugMode) {
        print('Sending login request to: $url');
        print('Request body: $body');
      }

      final response = await http
          .post(url, headers: ApiConfig.defaultHeaders, body: body)
          .timeout(
            ApiConfig.connectionTimeout,
            onTimeout: () {
              throw Exception('انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.');
            },
          );

      if (ApiConfig.enableDebugMode) {
        print('Response status: ${response.statusCode}');
        print('Response body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (ApiConfig.enableDebugMode) {
          print('Parsed response data: $responseData');
        }

        // التحقق من وجود خطأ في الاستجابة
        if (responseData.containsKey('error')) {
          return {
            'success': false,
            'message': responseData['error']['message'] ?? 'حدث خطأ غير معروف',
            'error_code': responseData['error']['code'],
          };
        }

        // التحقق من نجاح العملية
        if (responseData.containsKey('result')) {
          final outerResult = responseData['result'];

          if (ApiConfig.enableDebugMode) {
            print('Outer result: $outerResult');
          }

          // التحقق من وجود result مضاعف (nested result)
          // يمكن أن تكون الاستجابة في أحد التنسيقات التالية:
          // 1. {result: {success: true, ...}}
          // 2. {result: {result: {success: true, ...}}}
          Map<String, dynamic> result;
          if (outerResult.containsKey('result') &&
              outerResult['result'] is Map) {
            result = outerResult['result'];
          } else {
            result = outerResult;
          }

          if (ApiConfig.enableDebugMode) {
            print('Final result: $result');
            print('Success value: ${result['success']}');
            print('Success type: ${result['success'].runtimeType}');
            print('Result contains success: ${result.containsKey('success')}');
            print('Result keys: ${result.keys.toList()}');
          }

          // التحقق من وجود حقل success والتأكد من قيمته
          if (result.containsKey('success') && result['success'] == true) {
            if (ApiConfig.enableDebugMode) {
              print('Login successful, returning success response');
              print('Session ID: ${result['session_id']}');
              print('User ID: ${result['user_id']}');
            }

            // التأكد من وجود البيانات المطلوبة
            if (result['session_id'] == null || result['user_id'] == null) {
              if (ApiConfig.enableDebugMode) {
                print('Warning: Missing session_id or user_id in response');
              }
              return {
                'success': false,
                'message':
                    'استجابة غير مكتملة من الخادم - مفقود session_id أو user_id',
              };
            }

            return {
              'success': true,
              'message': result['message'] ?? 'تم تسجيل الدخول بنجاح',
              'session_id': result['session_id'],
              'user_id': result['user_id'],
            };
          } else {
            if (ApiConfig.enableDebugMode) {
              print('Login failed, success is not true');
            }
            return {
              'success': false,
              'message': result['message'] ?? 'فشل في تسجيل الدخول',
            };
          }
        }

        return {'success': false, 'message': 'استجابة غير متوقعة من الخادم'};
      } else {
        return {
          'success': false,
          'message': 'خطأ في الاتصال بالخادم (${response.statusCode})',
        };
      }
    } catch (e) {
      if (ApiConfig.enableDebugMode) {
        print('Login error: $e');
      }
      return {'success': false, 'message': 'خطأ في الاتصال: ${e.toString()}'};
    }
  }

  /// جلب بيانات الموظف الحالي باستخدام Odoo session authentication
  static Future<Map<String, dynamic>> getEmployeeData({
    required String sessionId,
    String? database,
    String? userId,
  }) async {
    try {
      final url = Uri.parse(ApiConfig.getFullUrl(ApiConfig.employeeEndpoint));

      if (ApiConfig.enableDebugMode) {
        print('Sending employee data request to: $url');
        print('Session ID: $sessionId');
        print('Database: $database');
      }

      // استخدام GET request مع session_id في Cookie header
      // هذا يتوافق مع auth='user' في Odoo
      final response = await http
          .get(
            url,
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Cookie': 'session_id=$sessionId',
              // إضافة database في header إذا كان متوفراً
              if (database != null && database.isNotEmpty)
                'X-Openerp-Dbname': database,
            },
          )
          .timeout(
            ApiConfig.connectionTimeout,
            onTimeout: () {
              throw Exception('انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.');
            },
          );

      if (ApiConfig.enableDebugMode) {
        print('Employee data response status: ${response.statusCode}');
        print('Employee data response body: ${response.body}');
      }

      if (response.statusCode == 200) {
        try {
          final Map<String, dynamic> responseData = jsonDecode(response.body);

          if (ApiConfig.enableDebugMode) {
            print('Parsed employee response: $responseData');
          }

          // التحقق من وجود خطأ في الاستجابة
          if (responseData.containsKey('error')) {
            final errorMessage = responseData['error'] is String
                ? responseData['error']
                : 'حدث خطأ في جلب بيانات الموظف';

            if (ApiConfig.enableDebugMode) {
              print('API Error: $errorMessage');
            }

            return {
              'success': false,
              'message': errorMessage,
              'requires_login': errorMessage.contains('الموظف غير مرتبط'),
            };
          }

          // التحقق من وجود البيانات المطلوبة
          // API الجديد يرجع البيانات مباشرة بدون تعقيد JSON-RPC
          if (responseData.containsKey('int_id') || responseData.containsKey('name')) {
            if (ApiConfig.enableDebugMode) {
              print('Successfully extracted employee data: $responseData');
            }
            return {'success': true, 'data': responseData};
          }

          if (ApiConfig.enableDebugMode) {
            print('No valid employee data found in response');
            print('Response keys: ${responseData.keys.toList()}');
          }

          return {
            'success': false,
            'message': 'لم يتم العثور على بيانات الموظف في الاستجابة',
          };
        } catch (e) {
          if (ApiConfig.enableDebugMode) {
            print('Error parsing JSON response: $e');
            print('Raw response: ${response.body}');
          }

          return {
            'success': false,
            'message': 'خطأ في تحليل استجابة الخادم',
          };
        }
      } else if (response.statusCode == 401 || response.statusCode == 403) {
        return {
          'success': false,
          'message': 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.',
          'requires_login': true,
        };
      } else if (response.statusCode == 404) {
        return {
          'success': false,
          'message': 'API endpoint غير موجود. تحقق من إعدادات الخادم.',
        };
      } else {
        return {
          'success': false,
          'message': 'خطأ في الاتصال بالخادم (${response.statusCode})',
        };
      }
    } catch (e) {
      if (ApiConfig.enableDebugMode) {
        print('Employee data error: $e');
      }
      return {'success': false, 'message': 'خطأ في الاتصال: ${e.toString()}'};
    }
  }

  /// التحقق من صحة الاتصال بالخادم
  static Future<bool> checkServerConnection() async {
    try {
      final url = Uri.parse('${ApiConfig.baseUrl}/web/database/selector');
      final response = await http.get(url).timeout(const Duration(seconds: 10));
      return response.statusCode == 200;
    } catch (e) {
      if (ApiConfig.enableDebugMode) {
        print('Server connection error: $e');
      }
      return false;
    }
  }
}
