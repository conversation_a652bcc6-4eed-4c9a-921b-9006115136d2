import 'package:flutter_test/flutter_test.dart';
import 'package:bsic_bank/services/api_service.dart';
import 'package:bsic_bank/services/auth_service.dart';
import 'package:bsic_bank/config/api_config.dart';

void main() {
  group('Security API Tests', () {
    // تأكد من تحديث هذه القيم حسب بيئة الاختبار
    const testDatabase = 'test_db';
    const testUsername = 'admin';
    const testPassword = 'admin';

    test('Test Odoo Login API', () async {
      // اختبار تسجيل الدخول الجديد
      final result = await ApiService.loginOdoo(
        database: testDatabase,
        username: testUsername,
        password: testPassword,
      );

      print('Login result: $result');

      // التحقق من نجاح تسجيل الدخول
      expect(result['success'], isTrue);
      expect(result['session_id'], isNotNull);
      expect(result['user_id'], isNotNull);
    });

    test('Test Employee Data API', () async {
      // أولاً، سجل الدخول للحصول على session_id
      final loginResult = await ApiService.loginOdoo(
        database: testDatabase,
        username: testUsername,
        password: testPassword,
      );

      if (loginResult['success'] == true) {
        final sessionId = loginResult['session_id'];
        
        // اختبار جلب بيانات الموظف
        final employeeResult = await ApiService.getEmployeeData(
          sessionId: sessionId,
          database: testDatabase,
          userId: loginResult['user_id'].toString(),
        );

        print('Employee data result: $employeeResult');

        // التحقق من نجاح جلب البيانات
        expect(employeeResult['success'], isTrue);
        expect(employeeResult['data'], isNotNull);
        
        final employeeData = employeeResult['data'];
        expect(employeeData['name'], isNotNull);
        expect(employeeData['int_id'], isNotNull);
      } else {
        fail('Login failed: ${loginResult['message']}');
      }
    });

    test('Test AuthService Integration', () async {
      final authService = AuthService();

      // اختبار تسجيل الدخول عبر AuthService
      final result = await authService.login(
        testUsername,
        testPassword,
        database: testDatabase,
      );

      print('AuthService login result: $result');

      if (result['success'] == true) {
        // اختبار جلب المستخدم الحالي
        final currentUser = await authService.getCurrentUser();
        
        print('Current user: $currentUser');
        
        expect(currentUser, isNotNull);
        expect(currentUser!.name, isNotEmpty);
        expect(currentUser.id, isNotEmpty);

        // تنظيف - تسجيل الخروج
        await authService.logout();
      } else {
        print('Login failed (expected if no server): ${result['message']}');
      }
    });

    test('Test Mock Data Fallback', () async {
      final authService = AuthService();

      // اختبار البيانات التجريبية (بدون database)
      final result = await authService.login('admin', 'admin123');

      print('Mock login result: $result');

      expect(result['success'], isTrue);
      expect(result['message'], contains('تجريبي'));

      // اختبار جلب المستخدم التجريبي
      final currentUser = await authService.getCurrentUser();
      
      expect(currentUser, isNotNull);
      expect(currentUser!.name, isNotEmpty);

      // تنظيف
      await authService.logout();
    });

    test('Test API Configuration', () {
      // اختبار إعدادات API
      expect(ApiConfig.baseUrl, isNotEmpty);
      expect(ApiConfig.employeeEndpoint, equals('/api/employee/me'));
      expect(ApiConfig.connectionTimeout, isNotNull);
      
      // اختبار دالة getFullUrl
      final fullUrl = ApiConfig.getFullUrl('/test');
      expect(fullUrl, contains(ApiConfig.baseUrl));
      expect(fullUrl, endsWith('/test'));
      
      // اختبار التحقق من صحة URL
      expect(ApiConfig.isValidServerUrl('http://example.com'), isTrue);
      expect(ApiConfig.isValidServerUrl('https://example.com:8069'), isTrue);
      expect(ApiConfig.isValidServerUrl('invalid-url'), isFalse);
    });

    test('Test Error Handling', () async {
      // اختبار معالجة الأخطاء مع بيانات خاطئة
      final result = await ApiService.loginOdoo(
        database: 'invalid_db',
        username: 'invalid_user',
        password: 'invalid_password',
      );

      print('Error handling result: $result');

      expect(result['success'], isFalse);
      expect(result['message'], isNotNull);
    });
  });

  group('API Security Tests', () {
    test('Test Session Validation', () async {
      // اختبار الوصول بدون session صالح
      final result = await ApiService.getEmployeeData(
        sessionId: 'invalid_session',
        database: 'test_db',
      );

      print('Invalid session result: $result');

      expect(result['success'], isFalse);
      expect(result['requires_login'], isTrue);
    });

    test('Test Database Validation', () async {
      // اختبار تسجيل الدخول بقاعدة بيانات غير موجودة
      final result = await ApiService.loginOdoo(
        database: 'non_existent_db',
        username: 'admin',
        password: 'admin',
      );

      print('Invalid database result: $result');

      expect(result['success'], isFalse);
    });
  });
}
