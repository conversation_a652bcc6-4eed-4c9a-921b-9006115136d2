# نظام إدارة الموظفين - Employee Management System

تطبيق Flutter لإدارة الموظفين مع دعم كامل للربط مع Odoo باستخدام JSON-RPC.

## 🚀 الميزات الجديدة - Odoo RPC Integration

- 🔐 **مصادقة Odoo متقدمة**: ربط مباشر مع Odoo باستخدام JSON-RPC
- 👤 **جلب بيانات الموظف**: استخدام `execute_kw` لجلب بيانات آمنة
- 🛡️ **حماية متقدمة**: استخدام UID وRecord Rules للأمان
- 📱 **واجهة محدثة**: شاشات جديدة مخصصة للـ RPC
- 🧪 **اختبارات شاملة**: اختبارات كاملة للتكامل مع Odoo

## الميزات الأساسية

- 🔐 **نظام تسجيل دخول متقدم**: دعم Odoo RPC والبيانات التجريبية
- 📊 **لوحة تحكم شاملة**: عرض إحصائيات الموظفين والإجازات
- 👤 **إدارة الملف الشخصي**: عرض وتحديث بيانات الموظف
- 📅 **نظام الإجازات**: طلب إجازات وعرض الأرصدة
- 🎨 **واجهة عربية**: دعم كامل للغة العربية مع تصميم Material Design

## 🚀 البدء السريع - Odoo RPC

### تشغيل النسخة الجديدة (Odoo RPC)
```bash
# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق الجديد مع دعم Odoo RPC
flutter run lib/main_rpc.dart

# تشغيل الاختبارات
flutter test test/odoo_rpc_test.dart
```

### تشغيل النسخة الأصلية
```bash
# تشغيل التطبيق الأصلي
flutter run lib/main.dart
```

## إعداد المشروع

### المتطلبات
- Flutter SDK (3.8.1 أو أحدث)
- Dart SDK
- Android Studio أو VS Code
- خادم Odoo (للاستخدام الحقيقي)

### التثبيت
```bash
# استنساخ المشروع
git clone [repository-url]
cd Mobile

# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق (اختر النسخة المناسبة)
flutter run lib/main_rpc.dart  # للـ Odoo RPC
flutter run lib/main.dart      # للنسخة الأصلية
```

## 🔧 تكوين Odoo RPC

### إعداد الخادم
1. افتح ملف `lib/config/api_config.dart`
2. قم بتغيير `baseUrl` إلى رابط خادم Odoo:
```dart
static const String baseUrl = 'http://your-odoo-server:8069';
```

### استخدام Odoo RPC
1. في شاشة تسجيل الدخول، فعّل "استخدام API الحقيقي"
2. أدخل اسم قاعدة البيانات في Odoo
3. أدخل اسم المستخدم وكلمة المرور
4. سيتم استخدام JSON-RPC للمصادقة وجلب البيانات

### API Key المستخدم
```
88fca631891c8a6727da95fc798cbe57cc00748d
```

## البيانات التجريبية

للاختبار السريع، يمكنك استخدام البيانات التجريبية:

**أسماء المستخدمين المتاحة:**
- `admin` - كلمة المرور: `admin123`
- `ahmed.ali` - كلمة المرور: `password123`
- `sara.mohamed` - كلمة المرور: `password123`
- `omar.hassan` - كلمة المرور: `password123`

## 📁 هيكل المشروع

```
lib/
├── config/          # ملفات التكوين
├── models/          # نماذج البيانات
│   ├── employee.dart           # النموذج الأصلي
│   └── employee_rpc.dart       # نموذج Odoo RPC
├── providers/       # إدارة الحالة (Provider)
│   ├── auth_provider.dart      # المزود الأصلي
│   └── auth_rpc_provider.dart  # مزود Odoo RPC
├── screens/         # شاشات التطبيق
│   ├── login_screen.dart       # شاشة تسجيل الدخول الأصلية
│   ├── login_rpc_screen.dart   # شاشة تسجيل الدخول RPC
│   └── employee_rpc_screen.dart # شاشة عرض بيانات الموظف
├── services/        # خدمات API والبيانات
│   ├── api_service.dart        # خدمة API الأصلية
│   ├── auth_service.dart       # خدمة المصادقة الأصلية
│   ├── odoo_rpc_service.dart   # خدمة Odoo RPC الجديدة
│   └── auth_rpc_service.dart   # خدمة المصادقة RPC
├── utils/           # أدوات مساعدة
├── widgets/         # مكونات واجهة المستخدم
├── main.dart        # التطبيق الأصلي
└── main_rpc.dart    # التطبيق الجديد مع Odoo RPC
```

## 🔌 Odoo JSON-RPC API

### 1. تسجيل الدخول (Authentication)

```json
POST /jsonrpc
{
  "jsonrpc": "2.0",
  "method": "call",
  "params": {
    "service": "common",
    "method": "authenticate",
    "args": ["database_name", "username", "password", {}]
  },
  "id": 1
}
```

**الاستجابة:**
```json
{
  "jsonrpc": "2.0",
  "result": 123,  // UID المستخدم
  "id": 1
}
```

### 2. جلب بيانات الموظف

```json
POST /jsonrpc
{
  "jsonrpc": "2.0",
  "method": "call",
  "params": {
    "service": "object",
    "method": "execute_kw",
    "args": [
      "database_name",
      123,  // UID
      "password",
      "hr.employee",
      "search_read",
      [[["user_id", "=", 123]]],
      {"fields": ["name", "parent_id"], "limit": 1}
    ]
  },
  "id": 2
}
```

**الاستجابة:**
```json
{
  "jsonrpc": "2.0",
  "result": [
    {
      "id": 456,
      "name": "أحمد علي",
      "parent_id": [789, "محمد حسن"]
    }
  ],
  "id": 2
}
```

## 🧪 الاختبار

### تشغيل اختبارات Odoo RPC
```bash
flutter test test/odoo_rpc_test.dart
```

### تشغيل جميع الاختبارات
```bash
flutter test
```

## 📚 الوثائق الإضافية

- **`ODOO_RPC_GUIDE.md`** - دليل شامل لاستخدام Odoo RPC
- **`SECURITY_API_UPDATE.md`** - توثيق تحديثات الأمان
- **`FUTURE_APIS_EXAMPLE.md`** - أمثلة لإضافة APIs جديدة
- **`IMPLEMENTATION_SUMMARY.md`** - ملخص التنفيذ

## 🔒 الأمان والحماية

- ✅ استخدام UID للمصادقة
- ✅ تطبيق Record Rules في Odoo
- ✅ جلب بيانات الموظف المرتبط بالمستخدم فقط
- ✅ معالجة آمنة للأخطاء
- ✅ حفظ محلي آمن للجلسة

## 🚀 الميزات المستقبلية

- إضافة APIs للإجازات والحضور
- تحسين واجهة المستخدم
- إضافة إشعارات push
- دعم المزيد من موديلات Odoo

## 📞 الدعم

في حالة وجود مشاكل:
1. راجع ملف `ODOO_RPC_GUIDE.md`
2. تحقق من logs في وضع debug
3. اختبر الاتصال بخادم Odoo
4. راجع ملفات الاختبار للأمثلة

---

**✅ النظام جاهز للاستخدام مع Odoo!**
