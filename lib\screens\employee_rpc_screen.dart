import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/auth_rpc_provider.dart';
import '../utils/app_colors.dart';

/// شاشة عرض بيانات الموظف المجلوبة من Odoo RPC
class EmployeeRpcScreen extends StatefulWidget {
  final VoidCallback? onLogout;

  const EmployeeRpcScreen({super.key, this.onLogout});

  @override
  State<EmployeeRpcScreen> createState() => _EmployeeRpcScreenState();
}

class _EmployeeRpcScreenState extends State<EmployeeRpcScreen> {
  @override
  void initState() {
    super.initState();
    // جلب بيانات الموظف عند تحميل الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshEmployeeData();
    });
  }

  /// إعادة جلب بيانات الموظف
  Future<void> _refreshEmployeeData() async {
    final authProvider = Provider.of<AuthRpcProvider>(context, listen: false);
    final success = await authProvider.refreshEmployeeData();
    
    if (!success && mounted && authProvider.errorMessage != null) {
      _showErrorSnackBar(authProvider.errorMessage!);
    }
  }

  /// تسجيل الخروج
  Future<void> _logout() async {
    final authProvider = Provider.of<AuthRpcProvider>(context, listen: false);
    await authProvider.logout();
    widget.onLogout?.call();
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('بيانات الموظف'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // زر إعادة التحميل
          Consumer<AuthRpcProvider>(
            builder: (context, authProvider, child) {
              return IconButton(
                onPressed: authProvider.isLoading ? null : _refreshEmployeeData,
                icon: authProvider.isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(FontAwesomeIcons.arrowsRotate),
                tooltip: 'إعادة تحميل البيانات',
              );
            },
          ),
          // زر تسجيل الخروج
          IconButton(
            onPressed: _logout,
            icon: const Icon(FontAwesomeIcons.rightFromBracket),
            tooltip: 'تسجيل الخروج',
          ),
        ],
      ),
      body: Consumer<AuthRpcProvider>(
        builder: (context, authProvider, child) {
          if (authProvider.isLoading) {
            return _buildLoadingWidget();
          }

          if (!authProvider.isLoggedIn) {
            return _buildNotLoggedInWidget();
          }

          return RefreshIndicator(
            onRefresh: _refreshEmployeeData,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // معلومات الجلسة
                  _buildSessionInfoCard(authProvider),
                  
                  const SizedBox(height: 16),
                  
                  // بيانات الموظف
                  if (authProvider.hasEmployeeData && authProvider.currentEmployee != null)
                    _buildEmployeeDataCard(authProvider.currentEmployee!)
                  else
                    _buildNoEmployeeDataCard(),
                  
                  const SizedBox(height: 16),
                  
                  // معلومات تشخيصية (في وضع التطوير)
                  _buildDebugInfoCard(authProvider),
                  
                  const SizedBox(height: 16),
                  
                  // أزرار الإجراءات
                  _buildActionButtons(authProvider),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري تحميل البيانات...'),
        ],
      ),
    );
  }

  Widget _buildNotLoggedInWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FontAwesomeIcons.userSlash,
            size: 64,
            color: AppColors.grey400,
          ),
          const SizedBox(height: 16),
          const Text(
            'لم يتم تسجيل الدخول',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text('يرجى تسجيل الدخول للوصول للبيانات'),
        ],
      ),
    );
  }

  Widget _buildSessionInfoCard(AuthRpcProvider authProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(FontAwesomeIcons.circleInfo, 
                  color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'معلومات الجلسة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow('UID', authProvider.currentUid ?? 'غير محدد'),
            _buildInfoRow('حالة الاتصال', authProvider.isLoggedIn ? 'متصل' : 'غير متصل'),
            _buildInfoRow('نوع البيانات', 
              authProvider.currentUid?.startsWith('mock_') == true 
                ? 'تجريبية' 
                : 'حقيقية (Odoo RPC)'),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeeDataCard(employee) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(FontAwesomeIcons.userTie, 
                  color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'بيانات الموظف',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow('ID', employee.id.toString()),
            _buildInfoRow('الاسم', employee.name),
            _buildInfoRow('المدير المباشر', employee.managerDisplayText),
            if (employee.hasManager)
              _buildInfoRow('ID المدير', employee.managerId.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildNoEmployeeDataCard() {
    return Card(
      color: AppColors.warning.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              FontAwesomeIcons.triangleExclamation,
              color: AppColors.warning,
              size: 48,
            ),
            const SizedBox(height: 12),
            const Text(
              'لا توجد بيانات موظف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'لم يتم العثور على بيانات الموظف المرتبط بهذا المستخدم.\n'
              'تأكد من ربط المستخدم بسجل موظف في Odoo.',
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDebugInfoCard(AuthRpcProvider authProvider) {
    final debugInfo = authProvider.getDebugInfo();
    
    return Card(
      color: AppColors.grey100,
      child: ExpansionTile(
        leading: Icon(FontAwesomeIcons.bug, color: AppColors.grey600),
        title: const Text('معلومات تشخيصية'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: debugInfo.entries.map((entry) {
                return _buildInfoRow(entry.key, entry.value?.toString() ?? 'null');
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(AuthRpcProvider authProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton.icon(
          onPressed: authProvider.isLoading ? null : _refreshEmployeeData,
          icon: const Icon(FontAwesomeIcons.arrowsRotate),
          label: const Text('إعادة تحميل البيانات'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.secondary,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
        const SizedBox(height: 8),
        ElevatedButton.icon(
          onPressed: _logout,
          icon: const Icon(FontAwesomeIcons.rightFromBracket),
          label: const Text('تسجيل الخروج'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.error,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
        ],
      ),
    );
  }
}
