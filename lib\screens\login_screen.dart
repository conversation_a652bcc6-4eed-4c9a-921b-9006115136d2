import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/auth_provider.dart';
import '../utils/app_colors.dart';

/// شاشة تسجيل الدخول باستخدام Odoo RPC
class LoginScreen extends StatefulWidget {
  final VoidCallback? onLoginSuccess;

  const LoginScreen({super.key, this.onLoginSuccess});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _databaseController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  
  bool _obscurePassword = true;
  bool _useRealApi = false;
  String? _selectedUsername;
  bool _isTestingConnection = false;

  @override
  void dispose() {
    _databaseController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// تسجيل الدخول
  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      final success = await authProvider.login(
        _usernameController.text.trim(),
        _passwordController.text,
        database: _useRealApi ? _databaseController.text.trim() : null,
      );

      if (success && mounted) {
        widget.onLoginSuccess?.call();
      } else if (mounted && authProvider.errorMessage != null) {
        _showErrorSnackBar(authProvider.errorMessage!);
      }
    }
  }

  /// اختبار الاتصال بالخادم
  Future<void> _testConnection() async {
    setState(() {
      _isTestingConnection = true;
    });

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isConnected = await authProvider.checkServerConnection();

    setState(() {
      _isTestingConnection = false;
    });

    if (mounted) {
      if (isConnected) {
        _showSuccessSnackBar('تم الاتصال بالخادم بنجاح ✅');
      } else {
        _showErrorSnackBar('فشل في الاتصال بالخادم. تحقق من الإعدادات.');
      }
    }
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// اختيار اسم مستخدم تجريبي
  void _selectMockUser(String username) {
    setState(() {
      _selectedUsername = username;
      _usernameController.text = username;
      
      // تعيين كلمة المرور التجريبية
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final usernames = authProvider.getAvailableUsernames();
      if (usernames.contains(username)) {
        switch (username) {
          case 'admin':
            _passwordController.text = 'admin123';
            break;
          case 'ahmed.ali':
          case 'sara.mohamed':
          case 'omar.hassan':
            _passwordController.text = 'password123';
            break;
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 60),
                
                // شعار التطبيق
                _buildLogo(),
                
                const SizedBox(height: 40),
                
                // عنوان تسجيل الدخول
                _buildTitle(),
                
                const SizedBox(height: 30),
                
                // مفتاح استخدام API الحقيقي
                _buildApiToggle(),
                
                const SizedBox(height: 20),
                
                // حقل قاعدة البيانات (للـ API الحقيقي فقط)
                if (_useRealApi) ...[
                  _buildDatabaseField(),
                  const SizedBox(height: 16),
                  _buildConnectionTestButton(),
                  const SizedBox(height: 20),
                ],
                
                // حقل اسم المستخدم
                _buildUsernameField(),
                
                const SizedBox(height: 16),
                
                // حقل كلمة المرور
                _buildPasswordField(),
                
                const SizedBox(height: 24),
                
                // زر تسجيل الدخول
                _buildLoginButton(),
                
                const SizedBox(height: 20),
                
                // أسماء المستخدمين التجريبية (للوضع التجريبي فقط)
                if (!_useRealApi) _buildMockUsersList(),
                
                const SizedBox(height: 20),
                
                // معلومات إضافية
                _buildInfoCard(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return Container(
      height: 100,
      decoration: BoxDecoration(
        color: AppColors.primary,
        shape: BoxShape.circle,
      ),
      child: const Icon(
        FontAwesomeIcons.userTie,
        size: 50,
        color: Colors.white,
      ),
    );
  }

  Widget _buildTitle() {
    return Column(
      children: [
        Text(
          'نظام إدارة الموظفين',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'تسجيل الدخول باستخدام Odoo RPC',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildApiToggle() {
    return Card(
      child: SwitchListTile(
        title: const Text('استخدام API الحقيقي'),
        subtitle: Text(_useRealApi 
          ? 'الاتصال بخادم Odoo' 
          : 'استخدام البيانات التجريبية'),
        value: _useRealApi,
        onChanged: (value) {
          setState(() {
            _useRealApi = value;
            if (!value) {
              _databaseController.clear();
            }
          });
        },
        activeColor: AppColors.primary,
      ),
    );
  }

  Widget _buildDatabaseField() {
    return TextFormField(
      controller: _databaseController,
      decoration: InputDecoration(
        labelText: 'اسم قاعدة البيانات',
        hintText: 'أدخل اسم قاعدة البيانات',
        prefixIcon: const Icon(FontAwesomeIcons.database),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (_useRealApi && (value == null || value.trim().isEmpty)) {
          return 'يرجى إدخال اسم قاعدة البيانات';
        }
        return null;
      },
    );
  }

  Widget _buildConnectionTestButton() {
    return ElevatedButton.icon(
      onPressed: _isTestingConnection ? null : _testConnection,
      icon: _isTestingConnection 
        ? const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          )
        : const Icon(FontAwesomeIcons.wifi),
      label: Text(_isTestingConnection ? 'جاري الاختبار...' : 'اختبار الاتصال'),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.secondary,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildUsernameField() {
    return TextFormField(
      controller: _usernameController,
      decoration: InputDecoration(
        labelText: 'اسم المستخدم',
        hintText: 'أدخل اسم المستخدم',
        prefixIcon: const Icon(FontAwesomeIcons.user),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال اسم المستخدم';
        }
        return null;
      },
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      decoration: InputDecoration(
        labelText: 'كلمة المرور',
        hintText: 'أدخل كلمة المرور',
        prefixIcon: const Icon(FontAwesomeIcons.lock),
        suffixIcon: IconButton(
          icon: Icon(_obscurePassword 
            ? FontAwesomeIcons.eyeSlash 
            : FontAwesomeIcons.eye),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال كلمة المرور';
        }
        return null;
      },
    );
  }

  Widget _buildLoginButton() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return ElevatedButton(
          onPressed: authProvider.isLoading ? null : _login,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: authProvider.isLoading
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 12),
                  Text('جاري تسجيل الدخول...'),
                ],
              )
            : const Text(
                'تسجيل الدخول',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
        );
      },
    );
  }

  Widget _buildMockUsersList() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final usernames = authProvider.getAvailableUsernames();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'أسماء المستخدمين التجريبية:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: usernames.map((username) {
                final isSelected = _selectedUsername == username;
                return FilterChip(
                  label: Text(username),
                  selected: isSelected,
                  onSelected: (_) => _selectMockUser(username),
                  selectedColor: AppColors.primary.withOpacity(0.2),
                  checkmarkColor: AppColors.primary,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      color: AppColors.info.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(FontAwesomeIcons.circleInfo, 
                  color: AppColors.info, size: 16),
                const SizedBox(width: 8),
                const Text(
                  'معلومات مهمة:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              '• للاستخدام الحقيقي: فعّل "استخدام API الحقيقي" وأدخل بيانات Odoo\n'
              '• للاختبار: استخدم البيانات التجريبية\n'
              '• يتم جلب بيانات الموظف تلقائياً بعد تسجيل الدخول\n'
              '• يجب ربط المستخدم بسجل موظف في Odoo\n'
              '• API Key: 88fca631891c8a6727da95fc798cbe57cc00748d',
              style: TextStyle(fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }
}
