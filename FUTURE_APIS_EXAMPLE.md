# أمثلة على APIs إضافية - Future APIs Examples

## نظرة عامة

بعد تطبيق API الحماية الجديد، يمكن إضافة المزيد من APIs باستخدام نفس النمط. هذا الملف يحتوي على أمثلة لـ APIs إضافية.

## 1. API الإجازات - Leaves API

### Python (Odoo Controller)

```python
from odoo import http
from odoo.http import request
import json
from datetime import datetime

class EmployeeLeaveAPI(http.Controller):

    @http.route('/api/employee/leaves', type='http', auth='user', csrf=False)
    def get_employee_leaves(self):
        """جلب إجازات الموظف الحالي"""
        employee = request.env.user.employee_id

        if not employee:
            return request.make_response(
                json.dumps({'error': 'الموظف غير مرتبط بالمستخدم الحالي'}),
                headers=[('Content-Type', 'application/json')]
            )

        # جلب طلبات الإجازات
        leave_requests = request.env['hr.leave'].search([
            ('employee_id', '=', employee.id)
        ], order='create_date desc', limit=50)

        leaves_data = []
        for leave in leave_requests:
            leaves_data.append({
                'id': leave.id,
                'name': leave.name,
                'holiday_status_id': [leave.holiday_status_id.id, leave.holiday_status_id.name],
                'request_date_from': leave.request_date_from.isoformat() if leave.request_date_from else None,
                'request_date_to': leave.request_date_to.isoformat() if leave.request_date_to else None,
                'number_of_days': leave.number_of_days,
                'state': leave.state,
                'date_from': leave.date_from.isoformat() if leave.date_from else None,
                'date_to': leave.date_to.isoformat() if leave.date_to else None,
            })

        result = {
            'leaves': leaves_data,
            'total_count': len(leaves_data)
        }

        return request.make_response(json.dumps(result), headers=[('Content-Type', 'application/json')])

    @http.route('/api/employee/leave_balance', type='http', auth='user', csrf=False)
    def get_leave_balance(self):
        """جلب أرصدة الإجازات"""
        employee = request.env.user.employee_id

        if not employee:
            return request.make_response(
                json.dumps({'error': 'الموظف غير مرتبط بالمستخدم الحالي'}),
                headers=[('Content-Type', 'application/json')]
            )

        # جلب أنواع الإجازات المتاحة
        leave_types = request.env['hr.leave.type'].search([])
        
        balance_data = []
        for leave_type in leave_types:
            # حساب الرصيد المتاح
            allocations = request.env['hr.leave.allocation'].search([
                ('employee_id', '=', employee.id),
                ('holiday_status_id', '=', leave_type.id),
                ('state', '=', 'validate')
            ])
            
            used_leaves = request.env['hr.leave'].search([
                ('employee_id', '=', employee.id),
                ('holiday_status_id', '=', leave_type.id),
                ('state', '=', 'validate')
            ])

            total_allocated = sum(allocations.mapped('number_of_days'))
            total_used = sum(used_leaves.mapped('number_of_days'))
            remaining = total_allocated - total_used

            balance_data.append({
                'leave_type_id': leave_type.id,
                'leave_type_name': leave_type.name,
                'allocated': total_allocated,
                'used': total_used,
                'remaining': remaining,
                'limit': leave_type.allocation_type == 'fixed_allocation'
            })

        result = {
            'balances': balance_data,
            'employee_name': employee.name
        }

        return request.make_response(json.dumps(result), headers=[('Content-Type', 'application/json')])

    @http.route('/api/employee/request_leave', type='http', auth='user', csrf=False, methods=['POST'])
    def request_leave(self):
        """طلب إجازة جديدة"""
        employee = request.env.user.employee_id

        if not employee:
            return request.make_response(
                json.dumps({'error': 'الموظف غير مرتبط بالمستخدم الحالي'}),
                headers=[('Content-Type', 'application/json')]
            )

        try:
            # قراءة البيانات من الطلب
            data = json.loads(request.httprequest.data.decode('utf-8'))
            
            # التحقق من البيانات المطلوبة
            required_fields = ['leave_type_id', 'date_from', 'date_to', 'name']
            for field in required_fields:
                if field not in data:
                    return request.make_response(
                        json.dumps({'error': f'الحقل {field} مطلوب'}),
                        headers=[('Content-Type', 'application/json')]
                    )

            # إنشاء طلب الإجازة
            leave_vals = {
                'employee_id': employee.id,
                'holiday_status_id': data['leave_type_id'],
                'request_date_from': datetime.fromisoformat(data['date_from']),
                'request_date_to': datetime.fromisoformat(data['date_to']),
                'name': data['name'],
                'request_unit_half': data.get('half_day', False),
                'request_unit_hours': data.get('hours_request', False),
            }

            leave_request = request.env['hr.leave'].create(leave_vals)
            
            # إرسال للموافقة إذا كان مطلوباً
            if data.get('submit_for_approval', True):
                leave_request.action_confirm()

            result = {
                'success': True,
                'message': 'تم إنشاء طلب الإجازة بنجاح',
                'leave_id': leave_request.id,
                'state': leave_request.state
            }

            return request.make_response(json.dumps(result), headers=[('Content-Type', 'application/json')])

        except Exception as e:
            return request.make_response(
                json.dumps({'error': f'خطأ في إنشاء طلب الإجازة: {str(e)}'}),
                headers=[('Content-Type', 'application/json')]
            )
```

### Flutter (Dart) - إضافة للـ ApiService

```dart
// في lib/services/api_service.dart

/// جلب إجازات الموظف
static Future<Map<String, dynamic>> getEmployeeLeaves({
  required String sessionId,
  String? database,
}) async {
  try {
    final url = Uri.parse('${ApiConfig.baseUrl}/api/employee/leaves');

    final response = await http.get(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': 'session_id=$sessionId',
        if (database != null && database.isNotEmpty)
          'X-Openerp-Dbname': database,
      },
    ).timeout(ApiConfig.connectionTimeout);

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      
      if (responseData.containsKey('error')) {
        return {
          'success': false,
          'message': responseData['error'],
        };
      }

      return {
        'success': true,
        'data': responseData,
      };
    } else if (response.statusCode == 401 || response.statusCode == 403) {
      return {
        'success': false,
        'message': 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.',
        'requires_login': true,
      };
    } else {
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم (${response.statusCode})',
      };
    }
  } catch (e) {
    return {
      'success': false,
      'message': 'خطأ في الاتصال: ${e.toString()}',
    };
  }
}

/// جلب أرصدة الإجازات
static Future<Map<String, dynamic>> getLeaveBalance({
  required String sessionId,
  String? database,
}) async {
  try {
    final url = Uri.parse('${ApiConfig.baseUrl}/api/employee/leave_balance');

    final response = await http.get(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': 'session_id=$sessionId',
        if (database != null && database.isNotEmpty)
          'X-Openerp-Dbname': database,
      },
    ).timeout(ApiConfig.connectionTimeout);

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      
      if (responseData.containsKey('error')) {
        return {
          'success': false,
          'message': responseData['error'],
        };
      }

      return {
        'success': true,
        'data': responseData,
      };
    } else {
      return {
        'success': false,
        'message': 'خطأ في جلب أرصدة الإجازات',
      };
    }
  } catch (e) {
    return {
      'success': false,
      'message': 'خطأ في الاتصال: ${e.toString()}',
    };
  }
}

/// طلب إجازة جديدة
static Future<Map<String, dynamic>> requestLeave({
  required String sessionId,
  String? database,
  required Map<String, dynamic> leaveData,
}) async {
  try {
    final url = Uri.parse('${ApiConfig.baseUrl}/api/employee/request_leave');

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': 'session_id=$sessionId',
        if (database != null && database.isNotEmpty)
          'X-Openerp-Dbname': database,
      },
      body: jsonEncode(leaveData),
    ).timeout(ApiConfig.connectionTimeout);

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      
      if (responseData.containsKey('error')) {
        return {
          'success': false,
          'message': responseData['error'],
        };
      }

      return {
        'success': true,
        'data': responseData,
      };
    } else {
      return {
        'success': false,
        'message': 'خطأ في إرسال طلب الإجازة',
      };
    }
  } catch (e) {
    return {
      'success': false,
      'message': 'خطأ في الاتصال: ${e.toString()}',
    };
  }
}
```

## 2. نماذج البيانات الجديدة

```dart
// lib/models/leave_request_new.dart
class LeaveRequestNew {
  final int id;
  final String name;
  final String leaveTypeName;
  final DateTime dateFrom;
  final DateTime dateTo;
  final double numberOfDays;
  final String state;

  LeaveRequestNew({
    required this.id,
    required this.name,
    required this.leaveTypeName,
    required this.dateFrom,
    required this.dateTo,
    required this.numberOfDays,
    required this.state,
  });

  factory LeaveRequestNew.fromJson(Map<String, dynamic> json) {
    return LeaveRequestNew(
      id: json['id'],
      name: json['name'],
      leaveTypeName: json['holiday_status_id'][1],
      dateFrom: DateTime.parse(json['date_from']),
      dateTo: DateTime.parse(json['date_to']),
      numberOfDays: json['number_of_days'].toDouble(),
      state: json['state'],
    );
  }
}

// lib/models/leave_balance_new.dart
class LeaveBalanceNew {
  final int leaveTypeId;
  final String leaveTypeName;
  final double allocated;
  final double used;
  final double remaining;
  final bool hasLimit;

  LeaveBalanceNew({
    required this.leaveTypeId,
    required this.leaveTypeName,
    required this.allocated,
    required this.used,
    required this.remaining,
    required this.hasLimit,
  });

  factory LeaveBalanceNew.fromJson(Map<String, dynamic> json) {
    return LeaveBalanceNew(
      leaveTypeId: json['leave_type_id'],
      leaveTypeName: json['leave_type_name'],
      allocated: json['allocated'].toDouble(),
      used: json['used'].toDouble(),
      remaining: json['remaining'].toDouble(),
      hasLimit: json['limit'],
    );
  }
}
```

## 3. استخدام APIs الجديدة

```dart
// في Provider أو Service
class LeaveService {
  final AuthService _authService = AuthService();

  Future<List<LeaveRequestNew>> getEmployeeLeaves() async {
    final sessionId = await _authService.getSessionId();
    final database = await _authService.getDatabase();

    if (sessionId != null) {
      final result = await ApiService.getEmployeeLeaves(
        sessionId: sessionId,
        database: database,
      );

      if (result['success'] == true) {
        final leavesData = result['data']['leaves'] as List;
        return leavesData.map((json) => LeaveRequestNew.fromJson(json)).toList();
      }
    }

    return [];
  }

  Future<List<LeaveBalanceNew>> getLeaveBalances() async {
    final sessionId = await _authService.getSessionId();
    final database = await _authService.getDatabase();

    if (sessionId != null) {
      final result = await ApiService.getLeaveBalance(
        sessionId: sessionId,
        database: database,
      );

      if (result['success'] == true) {
        final balancesData = result['data']['balances'] as List;
        return balancesData.map((json) => LeaveBalanceNew.fromJson(json)).toList();
      }
    }

    return [];
  }
}
```

## الخلاصة

هذا النمط يمكن تطبيقه على أي API إضافي:
1. إنشاء Controller في Odoo مع `auth='user'`
2. إضافة دالة في `ApiService` 
3. إنشاء نماذج البيانات المناسبة
4. استخدام نفس نظام المصادقة والـ session management

جميع APIs ستكون محمية ومؤمنة باستخدام نظام Odoo المدمج.
