import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';

/// خدمة للتعامل مع Odoo باستخدام JSON-RPC
class OdooRpcService {
  // معلومات الاتصال
  String? _uid;
  String? _database;
  String? _username;
  String? _password;

  // Getters للوصول للمعلومات
  String? get uid => _uid;
  String? get database => _database;
  bool get isAuthenticated => _uid != null;

  /// تسجيل الدخول والحصول على UID
  /// 
  /// [database] - اسم قاعدة البيانات
  /// [username] - اسم المستخدم
  /// [password] - كلمة المرور
  /// 
  /// Returns: Map يحتوي على success و uid أو رسالة خطأ
  Future<Map<String, dynamic>> authenticate({
    required String database,
    required String username,
    required String password,
  }) async {
    try {
      // تحضير URL للمصادقة
      final url = Uri.parse('${ApiConfig.baseUrl}/jsonrpc');
      
      // تحضير بيانات الطلب
      final requestData = {
        'jsonrpc': '2.0',
        'method': 'call',
        'params': {
          'service': 'common',
          'method': 'authenticate',
          'args': [database, username, password, {}]
        },
        'id': DateTime.now().millisecondsSinceEpoch,
      };

      if (ApiConfig.enableDebugMode) {
        print('🔐 محاولة تسجيل الدخول...');
        print('📡 URL: $url');
        print('📤 Request: ${jsonEncode(requestData)}');
      }

      // إرسال الطلب
      final response = await http
          .post(
            url,
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode(requestData),
          )
          .timeout(
            ApiConfig.connectionTimeout,
            onTimeout: () {
              throw Exception('انتهت مهلة الاتصال. تحقق من اتصال الإنترنت.');
            },
          );

      if (ApiConfig.enableDebugMode) {
        print('📥 Response Status: ${response.statusCode}');
        print('📥 Response Body: ${response.body}');
      }

      // التحقق من حالة الاستجابة
      if (response.statusCode != 200) {
        return {
          'success': false,
          'message': 'خطأ في الاتصال بالخادم (${response.statusCode})',
        };
      }

      // تحليل الاستجابة
      final Map<String, dynamic> responseData = jsonDecode(response.body);

      // التحقق من وجود خطأ في الاستجابة
      if (responseData.containsKey('error')) {
        final error = responseData['error'];
        String errorMessage = 'حدث خطأ في تسجيل الدخول';
        
        if (error is Map && error.containsKey('data')) {
          final errorData = error['data'];
          if (errorData is Map && errorData.containsKey('message')) {
            errorMessage = errorData['message'];
          }
        }

        return {
          'success': false,
          'message': errorMessage,
        };
      }

      // استخراج UID من النتيجة
      final result = responseData['result'];
      
      if (result == null || result == false) {
        return {
          'success': false,
          'message': 'اسم المستخدم أو كلمة المرور غير صحيحة',
        };
      }

      // حفظ معلومات المصادقة
      _uid = result.toString();
      _database = database;
      _username = username;
      _password = password;

      if (ApiConfig.enableDebugMode) {
        print('✅ تم تسجيل الدخول بنجاح - UID: $_uid');
      }

      return {
        'success': true,
        'uid': _uid,
        'message': 'تم تسجيل الدخول بنجاح',
      };

    } catch (e) {
      if (ApiConfig.enableDebugMode) {
        print('❌ خطأ في تسجيل الدخول: $e');
      }
      
      return {
        'success': false,
        'message': 'خطأ في الاتصال: ${e.toString()}',
      };
    }
  }

  /// جلب بيانات الموظف الحالي
  /// 
  /// يستخدم execute_kw لجلب بيانات الموظف المرتبط بالمستخدم الحالي
  /// 
  /// Returns: Map يحتوي على success وبيانات الموظف أو رسالة خطأ
  Future<Map<String, dynamic>> getCurrentEmployeeData() async {
    // التحقق من المصادقة
    if (!isAuthenticated) {
      return {
        'success': false,
        'message': 'يجب تسجيل الدخول أولاً',
        'requires_login': true,
      };
    }

    try {
      // تحضير URL
      final url = Uri.parse('${ApiConfig.baseUrl}/jsonrpc');
      
      // تحضير بيانات الطلب لجلب بيانات الموظف
      final requestData = {
        'jsonrpc': '2.0',
        'method': 'call',
        'params': {
          'service': 'object',
          'method': 'execute_kw',
          'args': [
            _database,
            int.parse(_uid!),
            _password,
            'hr.employee',
            'search_read',
            [
              [['user_id', '=', int.parse(_uid!)]] // البحث عن الموظف المرتبط بالمستخدم الحالي
            ],
            {
              'fields': ['name', 'parent_id'], // الحقول المطلوبة
              'limit': 1, // موظف واحد فقط
            }
          ]
        },
        'id': DateTime.now().millisecondsSinceEpoch,
      };

      if (ApiConfig.enableDebugMode) {
        print('👤 جلب بيانات الموظف...');
        print('📤 Request: ${jsonEncode(requestData)}');
      }

      // إرسال الطلب
      final response = await http
          .post(
            url,
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode(requestData),
          )
          .timeout(
            ApiConfig.connectionTimeout,
            onTimeout: () {
              throw Exception('انتهت مهلة الاتصال');
            },
          );

      if (ApiConfig.enableDebugMode) {
        print('📥 Employee Response Status: ${response.statusCode}');
        print('📥 Employee Response Body: ${response.body}');
      }

      // التحقق من حالة الاستجابة
      if (response.statusCode != 200) {
        return {
          'success': false,
          'message': 'خطأ في الاتصال بالخادم (${response.statusCode})',
        };
      }

      // تحليل الاستجابة
      final Map<String, dynamic> responseData = jsonDecode(response.body);

      // التحقق من وجود خطأ
      if (responseData.containsKey('error')) {
        final error = responseData['error'];
        String errorMessage = 'حدث خطأ في جلب بيانات الموظف';
        
        if (error is Map && error.containsKey('data')) {
          final errorData = error['data'];
          if (errorData is Map && errorData.containsKey('message')) {
            errorMessage = errorData['message'];
          }
        }

        return {
          'success': false,
          'message': errorMessage,
        };
      }

      // استخراج بيانات الموظف
      final result = responseData['result'];
      
      if (result == null || (result is List && result.isEmpty)) {
        return {
          'success': false,
          'message': 'لم يتم العثور على بيانات الموظف. تأكد من ربط المستخدم بسجل موظف.',
        };
      }

      // استخراج بيانات الموظف الأول (يجب أن يكون واحد فقط)
      final employeeData = (result as List).first;

      if (ApiConfig.enableDebugMode) {
        print('✅ تم جلب بيانات الموظف بنجاح: $employeeData');
      }

      return {
        'success': true,
        'data': employeeData,
        'message': 'تم جلب بيانات الموظف بنجاح',
      };

    } catch (e) {
      if (ApiConfig.enableDebugMode) {
        print('❌ خطأ في جلب بيانات الموظف: $e');
      }
      
      return {
        'success': false,
        'message': 'خطأ في جلب البيانات: ${e.toString()}',
      };
    }
  }

  /// تسجيل الخروج
  void logout() {
    _uid = null;
    _database = null;
    _username = null;
    _password = null;
    
    if (ApiConfig.enableDebugMode) {
      print('🚪 تم تسجيل الخروج');
    }
  }

  /// التحقق من صحة الاتصال بالخادم
  Future<bool> checkServerConnection() async {
    try {
      final url = Uri.parse('${ApiConfig.baseUrl}/jsonrpc');
      
      // طلب بسيط للتحقق من الاتصال
      final requestData = {
        'jsonrpc': '2.0',
        'method': 'call',
        'params': {
          'service': 'common',
          'method': 'version',
          'args': []
        },
        'id': 1,
      };

      final response = await http
          .post(
            url,
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode(requestData),
          )
          .timeout(const Duration(seconds: 10));

      return response.statusCode == 200;
    } catch (e) {
      if (ApiConfig.enableDebugMode) {
        print('❌ فشل في الاتصال بالخادم: $e');
      }
      return false;
    }
  }
}
