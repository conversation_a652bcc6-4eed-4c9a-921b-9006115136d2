import 'package:shared_preferences/shared_preferences.dart';
import '../models/employee.dart';
import 'odoo_rpc_service.dart';
import 'mock_data_service.dart';

/// خدمة المصادقة للعمل مع Odoo RPC
class AuthService {
  static const String _isLoggedInKey = 'rpc_isLoggedIn';
  static const String _uidKey = 'rpc_uid';
  static const String _databaseKey = 'rpc_database';
  static const String _usernameKey = 'rpc_username';
  static const String _employeeDataKey = 'rpc_employeeData';

  final OdooRpcService _rpcService = OdooRpcService();

  // بيانات تجريبية للاختبار (fallback)
  static const Map<String, String> _mockCredentials = {
    'admin': 'admin123',
    'ahmed.ali': 'password123',
    'sara.mohamed': 'password123',
    'omar.hassan': 'password123',
  };

  /// تسجيل الدخول باستخدام Odoo RPC أو البيانات التجريبية
  Future<Map<String, dynamic>> login(
    String username,
    String password, {
    String? database,
  }) async {
    try {
      // إذا تم تحديد قاعدة البيانات، استخدم Odoo RPC
      if (database != null && database.isNotEmpty) {
        return await _loginWithOdooRpc(username, password, database);
      } else {
        // استخدام البيانات التجريبية
        return await _loginWithMockData(username, password);
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في تسجيل الدخول: ${e.toString()}',
      };
    }
  }

  /// تسجيل الدخول باستخدام Odoo RPC
  Future<Map<String, dynamic>> _loginWithOdooRpc(
    String username,
    String password,
    String database,
  ) async {
    // محاولة المصادقة مع Odoo
    final authResult = await _rpcService.authenticate(
      database: database,
      username: username,
      password: password,
    );

    if (authResult['success'] != true) {
      return authResult; // إرجاع رسالة الخطأ
    }

    // جلب بيانات الموظف
    final employeeResult = await _rpcService.getCurrentEmployeeData();

    if (employeeResult['success'] != true) {
      // في حالة فشل جلب بيانات الموظف، لا نزال نعتبر تسجيل الدخول ناجحاً
      await _saveAuthData(
        uid: authResult['uid'],
        database: database,
        username: username,
        employeeData: null,
      );

      return {
        'success': true,
        'message': 'تم تسجيل الدخول بنجاح، لكن لم يتم العثور على بيانات الموظف',
        'uid': authResult['uid'],
        'has_employee_data': false,
      };
    }

    // حفظ جميع البيانات
    await _saveAuthData(
      uid: authResult['uid'],
      database: database,
      username: username,
      employeeData: employeeResult['data'],
    );

    return {
      'success': true,
      'message': 'تم تسجيل الدخول وجلب بيانات الموظف بنجاح',
      'uid': authResult['uid'],
      'has_employee_data': true,
      'employee_data': employeeResult['data'],
    };
  }

  /// تسجيل الدخول باستخدام البيانات التجريبية
  Future<Map<String, dynamic>> _loginWithMockData(
    String username,
    String password,
  ) async {
    if (_mockCredentials.containsKey(username) &&
        _mockCredentials[username] == password) {
      
      // حفظ بيانات تجريبية
      await _saveAuthData(
        uid: 'mock_$username',
        database: 'mock_database',
        username: username,
        employeeData: null, // سيتم جلبها من MockDataService
      );

      return {
        'success': true,
        'message': 'تم تسجيل الدخول بنجاح (وضع تجريبي)',
        'uid': 'mock_$username',
        'is_mock': true,
      };
    } else {
      return {
        'success': false,
        'message': 'اسم المستخدم أو كلمة المرور غير صحيحة',
      };
    }
  }

  /// حفظ بيانات المصادقة محلياً
  Future<void> _saveAuthData({
    required String uid,
    required String database,
    required String username,
    Map<String, dynamic>? employeeData,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setBool(_isLoggedInKey, true);
    await prefs.setString(_uidKey, uid);
    await prefs.setString(_databaseKey, database);
    await prefs.setString(_usernameKey, username);
    
    if (employeeData != null) {
      await prefs.setString(_employeeDataKey, employeeData.toString());
    }
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    
    // مسح البيانات المحلية
    await prefs.remove(_isLoggedInKey);
    await prefs.remove(_uidKey);
    await prefs.remove(_databaseKey);
    await prefs.remove(_usernameKey);
    await prefs.remove(_employeeDataKey);
    
    // تسجيل الخروج من RPC service
    _rpcService.logout();
  }

  /// التحقق من حالة تسجيل الدخول
  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isLoggedInKey) ?? false;
  }

  /// الحصول على UID الحالي
  Future<String?> getCurrentUid() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_uidKey);
  }

  /// الحصول على اسم قاعدة البيانات
  Future<String?> getDatabase() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_databaseKey);
  }

  /// الحصول على اسم المستخدم
  Future<String?> getUsername() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_usernameKey);
  }

  /// الحصول على بيانات الموظف الحالي
  Future<Employee?> getCurrentEmployee() async {
    final uid = await getCurrentUid();
    final database = await getDatabase();
    
    if (uid == null) return null;

    // التحقق من البيانات التجريبية
    if (uid.startsWith('mock_')) {
      final username = uid.replaceFirst('mock_', '');
      final mockEmployee = MockDataService.getEmployeeById(username);
      
      if (mockEmployee != null) {
        // تحويل من Employee إلى Employee
        return Employee(
          id: int.tryParse(mockEmployee.id) ?? 0,
          name: mockEmployee.name,
          managerName: mockEmployee.manager.isNotEmpty ? mockEmployee.manager : null,
          managerId: mockEmployee.manager.isNotEmpty ? 1 : null,
        );
      }
      return null;
    }

    // للبيانات الحقيقية، جلب من Odoo
    if (database != null && !database.startsWith('mock_')) {
      final employeeResult = await _rpcService.getCurrentEmployeeData();
      
      if (employeeResult['success'] == true && employeeResult['data'] != null) {
        return Employee.fromOdooRpc(employeeResult['data']);
      }
    }

    return null;
  }

  /// إعادة جلب بيانات الموظف من الخادم
  Future<Map<String, dynamic>> refreshEmployeeData() async {
    if (!await isLoggedIn()) {
      return {
        'success': false,
        'message': 'يجب تسجيل الدخول أولاً',
        'requires_login': true,
      };
    }

    final uid = await getCurrentUid();
    
    // للبيانات التجريبية، لا حاجة لإعادة الجلب
    if (uid != null && uid.startsWith('mock_')) {
      return {
        'success': true,
        'message': 'البيانات التجريبية محدثة',
      };
    }

    // للبيانات الحقيقية، جلب من Odoo
    return await _rpcService.getCurrentEmployeeData();
  }

  /// الحصول على قائمة أسماء المستخدمين التجريبية
  List<String> getAvailableUsernames() {
    return _mockCredentials.keys.toList();
  }

  /// التحقق من صحة الاتصال بالخادم
  Future<bool> checkServerConnection() async {
    return await _rpcService.checkServerConnection();
  }
}
