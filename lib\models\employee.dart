/// نموذج بيانات الموظف للعمل مع Odoo RPC
class Employee {
  final int id;
  final String name;
  final String? managerName;
  final int? managerId;

  Employee({
    required this.id,
    required this.name,
    this.managerName,
    this.managerId,
  });

  /// إنشاء كائن Employee من استجابة Odoo RPC
  /// 
  /// [json] - البيانات المستلمة من Odoo execute_kw
  factory Employee.fromOdooRpc(Map<String, dynamic> json) {
    // استخراج معلومات المدير من parent_id
    String? managerName;
    int? managerId;
    
    final parentId = json['parent_id'];
    if (parentId != null && parentId != false) {
      if (parentId is List && parentId.length >= 2) {
        // تنسيق Odoo: [id, name]
        managerId = parentId[0] as int?;
        managerName = parentId[1] as String?;
      } else if (parentId is int) {
        // في حالة وجود ID فقط
        managerId = parentId;
      }
    }

    return Employee(
      id: json['id'] as int,
      name: json['name'] as String? ?? 'غير محدد',
      managerName: managerName,
      managerId: managerId,
    );
  }

  /// تحويل إلى Map للحفظ أو الإرسال
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'manager_name': managerName,
      'manager_id': managerId,
    };
  }

  /// تحويل إلى String للطباعة
  @override
  String toString() {
    return 'Employee{id: $id, name: $name, manager: $managerName}';
  }

  /// التحقق من وجود مدير
  bool get hasManager => managerId != null && managerName != null;

  /// الحصول على نص وصفي للمدير
  String get managerDisplayText {
    if (hasManager) {
      return managerName!;
    }
    return 'لا يوجد مدير مباشر';
  }
}
